import mysql from 'mysql2/promise';
import * as path from 'path';
import * as fs from 'fs';

export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
}

// Default database configuration
const defaultConfig: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'messenger_app',
  connectionLimit: 10
};

// Database connection pool
let pool: mysql.Pool | null = null;

/**
 * Initialize database connection pool
 */
export async function initializeDatabase(config: DatabaseConfig = defaultConfig): Promise<mysql.Pool> {
  try {
    // Create connection pool
    pool = mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      waitForConnections: true,
      connectionLimit: config.connectionLimit,
      queueLimit: 0
    });

    // Test the connection
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();

    return pool;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

/**
 * Get database connection pool
 */
export function getDatabase(): mysql.Pool {
  if (!pool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pool;
}

/**
 * Execute database schema setup
 */
export async function setupDatabaseSchema(): Promise<void> {
  try {
    const schemaPath = path.join(__dirname, '../../database/schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Schema file not found at: ${schemaPath}`);
    }

    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split SQL statements by semicolon and execute them one by one
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    const db = getDatabase();
    
    for (const statement of statements) {
      try {
        await db.execute(statement);
      } catch (error) {
        // Log but don't throw for statements that might already exist
        console.warn('Schema statement warning:', error);
      }
    }

    console.log('✅ Database schema setup completed');
  } catch (error) {
    console.error('❌ Database schema setup failed:', error);
    throw error;
  }
}

/**
 * Close database connection
 */
export async function closeDatabaseConnection(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('✅ Database connection closed');
  }
}

/**
 * Execute a query with parameters
 */
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T> {
  try {
    const db = getDatabase();
    // Use query() instead of execute() to bypass prepared statement issues
    const [results] = await db.query(query, params);
    return results as T;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

/**
 * Execute a prepared statement
 */
export async function executePreparedStatement<T = any>(
  query: string,
  params: any[] = []
): Promise<T> {
  try {
    const db = getDatabase();
    const [results] = await db.execute(query, params);
    return results as T;
  } catch (error) {
    console.error('Database prepared statement error:', error);
    throw error;
  }
}

/**
 * Begin a database transaction
 */
export async function beginTransaction(): Promise<mysql.PoolConnection> {
  const db = getDatabase();
  const connection = await db.getConnection();
  await connection.beginTransaction();
  return connection;
}

/**
 * Commit a database transaction
 */
export async function commitTransaction(connection: mysql.PoolConnection): Promise<void> {
  await connection.commit();
  connection.release();
}

/**
 * Rollback a database transaction
 */
export async function rollbackTransaction(connection: mysql.PoolConnection): Promise<void> {
  await connection.rollback();
  connection.release();
}
