import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { initializeDatabase, setupDatabaseSchema } from '../database/config';
import { setupAuthRoutes } from './routes/auth';
import { setupUserRoutes } from './routes/users';
import { setupConversationRoutes } from './routes/conversations';
import { setupMessageRoutes } from './routes/messages';
import { setupFriendshipRoutes } from './routes/friendships';
import fileRoutes from '../routes/fileRoutes';
import { setupSocketHandlers } from './socket/socketHandlers';

// Load environment variables
dotenv.config();

export class MessengerServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private port: number;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.port = parseInt(process.env.SERVER_PORT || '3001');
    
    // Initialize Socket.IO
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
  }

  private setupMiddleware(): void {
    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "http://localhost:5173",
      credentials: true
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static file serving for uploads
    const uploadsDir = path.join(__dirname, '../../../uploads');
    this.app.use('/uploads', express.static(uploadsDir));

    // Request logging middleware
    this.app.use((req, _res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });

    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });
  }

  private setupRoutes(): void {
    // Test route
    this.app.get('/api/test', (_req, res) => {
      res.json({ success: true, message: 'Server is working!' });
    });

    // Static file serving for uploads
    this.app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

    // API routes
    setupAuthRoutes(this.app);
    setupUserRoutes(this.app);
    setupConversationRoutes(this.app);
    setupMessageRoutes(this.app);
    setupFriendshipRoutes(this.app);
    this.app.use('/api/files', fileRoutes);

    // 404 handler
    this.app.use((_req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found'
      });
    });

    // Global error handler
    this.app.use((error: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
      console.error('Server error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    });
  }

  private setupSocketHandlers(): void {
    setupSocketHandlers(this.io);
  }

  public async start(): Promise<void> {
    try {
      // Start server first
      this.server.listen(this.port, () => {
        console.log(`🚀 Messenger server running on port ${this.port}`);
        console.log(`📡 Socket.IO server ready for connections`);
        console.log(`🌐 CORS enabled for: ${process.env.CORS_ORIGIN}`);
        console.log(`📊 Health check available at: http://localhost:${this.port}/health`);
      });

      // Initialize database after server starts
      try {
        console.log('🔄 Initializing database connection...');
        await initializeDatabase();

        // Setup database schema
        console.log('🔄 Setting up database schema...');
        await setupDatabaseSchema();
        console.log('✅ Database initialized successfully');
      } catch (dbError) {
        console.warn('⚠️ Database initialization failed, continuing without database:', dbError);
      }

      // Handle graceful shutdown
      process.on('SIGTERM', this.shutdown.bind(this));
      process.on('SIGINT', this.shutdown.bind(this));

    } catch (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    console.log('🔄 Shutting down server...');
    
    // Close Socket.IO connections
    this.io.close();
    
    // Close HTTP server
    this.server.close(() => {
      console.log('✅ Server shut down successfully');
      process.exit(0);
    });
  }

  public getApp(): express.Application {
    return this.app;
  }

  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Create and export server instance
export const messengerServer = new MessengerServer();

// Start server if this file is run directly
if (require.main === module) {
  messengerServer.start().catch(error => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}
