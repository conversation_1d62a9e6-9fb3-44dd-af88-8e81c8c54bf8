<template>
  <div class="notification-settings">
    <h3 class="settings-title">Notification Settings</h3>
    
    <div class="settings-section">
      <div class="setting-item">
        <label class="setting-label">
          <input
            type="checkbox"
            v-model="settings.enabled"
            @change="updateSettings"
            class="setting-checkbox"
          />
          <span class="checkmark"></span>
          Enable notifications
        </label>
        <p class="setting-description">Show desktop notifications for new messages and events</p>
      </div>

      <div class="setting-item" :class="{ disabled: !settings.enabled }">
        <label class="setting-label">
          <input
            type="checkbox"
            v-model="settings.soundEnabled"
            @change="updateSettings"
            :disabled="!settings.enabled"
            class="setting-checkbox"
          />
          <span class="checkmark"></span>
          Enable notification sounds
        </label>
        <p class="setting-description">Play sound when notifications appear</p>
      </div>

      <div class="setting-item" :class="{ disabled: !settings.enabled }">
        <label class="setting-label">
          <input
            type="checkbox"
            v-model="settings.showPreview"
            @change="updateSettings"
            :disabled="!settings.enabled"
            class="setting-checkbox"
          />
          <span class="checkmark"></span>
          Show message preview
        </label>
        <p class="setting-description">Display message content in notifications</p>
      </div>

      <div class="setting-item" :class="{ disabled: !settings.enabled }">
        <label class="setting-label">
          <input
            type="checkbox"
            v-model="settings.onlyWhenUnfocused"
            @change="updateSettings"
            :disabled="!settings.enabled"
            class="setting-checkbox"
          />
          <span class="checkmark"></span>
          Only when app is unfocused
        </label>
        <p class="setting-description">Only show notifications when the app is not in focus</p>
      </div>
    </div>

    <div class="settings-actions">
      <button 
        class="test-button"
        @click="testNotification"
        :disabled="!settings.enabled"
      >
        Test Notification
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { notificationService, type NotificationSettings } from '../../services/notificationService'

const settings = ref<NotificationSettings>({
  enabled: true,
  soundEnabled: true,
  showPreview: true,
  onlyWhenUnfocused: true
})

onMounted(() => {
  settings.value = notificationService.getSettings()
})

const updateSettings = () => {
  notificationService.updateSettings(settings.value)
}

const testNotification = async () => {
  try {
    await notificationService.testNotification()
  } catch (error) {
    console.error('Failed to test notification:', error)
  }
}
</script>

<style scoped>
.notification-settings {
  padding: 20px;
  max-width: 500px;
}

.settings-title {
  margin: 0 0 20px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.settings-section {
  margin-bottom: 24px;
}

.setting-item {
  margin-bottom: 16px;
  transition: opacity 0.2s ease;
}

.setting-item.disabled {
  opacity: 0.5;
}

.setting-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  line-height: 1.5;
}

.setting-label:hover {
  color: #1f2937;
}

.setting-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  position: relative;
  width: 20px;
  height: 20px;
  background: #fff;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.setting-checkbox:checked ~ .checkmark {
  background: #6366f1;
  border-color: #6366f1;
}

.setting-checkbox:checked ~ .checkmark:after {
  display: block;
}

.setting-checkbox:disabled ~ .checkmark {
  background: #f3f4f6;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.setting-description {
  margin: 4px 0 0 32px;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.settings-actions {
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.test-button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-button:hover:not(:disabled) {
  background: #5856eb;
}

.test-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .settings-title {
    color: #f9fafb;
  }
  
  .setting-label {
    color: #d1d5db;
  }
  
  .setting-label:hover {
    color: #f9fafb;
  }
  
  .checkmark {
    background: #374151;
    border-color: #4b5563;
  }
  
  .setting-checkbox:disabled ~ .checkmark {
    background: #1f2937;
    border-color: #374151;
  }
  
  .setting-description {
    color: #9ca3af;
  }
  
  .settings-actions {
    border-color: #4b5563;
  }
}
</style>
