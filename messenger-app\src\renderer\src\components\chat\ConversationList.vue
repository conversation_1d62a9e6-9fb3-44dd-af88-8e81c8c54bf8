<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useChatStore } from '../../stores/chat'
import { useAuthStore } from '../../stores/auth'
import type { Conversation } from '../../stores/chat'

const chatStore = useChatStore()
const authStore = useAuthStore()

const searchQuery = ref('')

const filteredConversations = computed(() => {
  if (!searchQuery.value.trim()) {
    return chatStore.sortedConversations
  }

  const query = searchQuery.value.toLowerCase()
  return chatStore.sortedConversations.filter(conversation => {
    // Search by conversation name (for groups)
    if (conversation.name && conversation.name.toLowerCase().includes(query)) {
      return true
    }

    // Search by participant names
    return conversation.participants.some(participant => 
      participant.display_name.toLowerCase().includes(query) ||
      participant.username.toLowerCase().includes(query)
    )
  })
})

const getConversationDisplayName = (conversation: Conversation): string => {
  if (conversation.type === 'group' && conversation.name) {
    return conversation.name
  }

  // For direct conversations, show the other participant's name
  const otherParticipant = conversation.participants.find(
    p => p.id !== authStore.user?.id
  )
  return otherParticipant?.display_name || 'Unknown User'
}

const getConversationAvatar = (conversation: Conversation): string | null => {
  if (conversation.avatar_url) {
    return conversation.avatar_url
  }

  // For direct conversations, use the other participant's avatar
  if (conversation.type === 'direct') {
    const otherParticipant = conversation.participants.find(
      p => p.id !== authStore.user?.id
    )
    return otherParticipant?.avatar_url || null
  }

  return null
}

const getConversationInitials = (conversation: Conversation): string => {
  const displayName = getConversationDisplayName(conversation)
  return displayName.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
}

const formatLastMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor(diffInHours * 60)
    return diffInMinutes < 1 ? 'now' : `${diffInMinutes}m`
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return diffInDays === 1 ? '1d' : `${diffInDays}d`
  }
}

const truncateMessage = (content: string, maxLength: number = 50): string => {
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength) + '...'
}

const selectConversation = async (conversation: Conversation) => {
  await chatStore.setActiveConversation(conversation)
}

const isConversationActive = (conversation: Conversation): boolean => {
  return chatStore.activeConversation?.id === conversation.id
}

const getOnlineParticipantsCount = (conversation: Conversation): number => {
  return conversation.participants.filter(p => p.status === 'online').length
}

onMounted(() => {
  // Load conversations if not already loaded
  if (chatStore.conversations.length === 0) {
    chatStore.loadConversations()
  }
})
</script>

<template>
  <div class="conversation-list">
    <!-- Search Bar -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="M21 21l-4.35-4.35"/>
        </svg>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search conversations..."
          class="search-input"
        />
      </div>
      
      <button 
        class="new-conversation-button"
        @click="chatStore.showNewConversationModal = true"
        title="New Conversation"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
      </button>
    </div>

    <!-- Conversations List -->
    <div class="conversations-container">
      <div v-if="chatStore.isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading conversations...</p>
      </div>

      <div v-else-if="filteredConversations.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
        </div>
        <p class="empty-text">
          {{ searchQuery ? 'No conversations found' : 'No conversations yet' }}
        </p>
        <button 
          v-if="!searchQuery"
          class="start-conversation-button"
          @click="chatStore.showNewConversationModal = true"
        >
          Start a conversation
        </button>
      </div>

      <div v-else class="conversations-list">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          class="conversation-item"
          :class="{ 'active': isConversationActive(conversation) }"
          @click="selectConversation(conversation)"
        >
          <!-- Avatar -->
          <div class="conversation-avatar">
            <img 
              v-if="getConversationAvatar(conversation)"
              :src="getConversationAvatar(conversation)!"
              :alt="getConversationDisplayName(conversation)"
              class="avatar-image"
            />
            <div v-else class="avatar-placeholder">
              {{ getConversationInitials(conversation) }}
            </div>
            
            <!-- Online indicator for direct conversations -->
            <div 
              v-if="conversation.type === 'direct'"
              class="status-indicator"
              :class="conversation.participants.find(p => p.id !== authStore.user?.id)?.status"
            ></div>
            
            <!-- Group online count -->
            <div 
              v-else-if="conversation.type === 'group' && getOnlineParticipantsCount(conversation) > 0"
              class="online-count"
            >
              {{ getOnlineParticipantsCount(conversation) }}
            </div>
          </div>

          <!-- Conversation Info -->
          <div class="conversation-info">
            <div class="conversation-header">
              <h3 class="conversation-name">
                {{ getConversationDisplayName(conversation) }}
              </h3>
              <span 
                v-if="conversation.last_message"
                class="last-message-time"
              >
                {{ formatLastMessageTime(conversation.last_message.created_at) }}
              </span>
            </div>

            <div class="conversation-preview">
              <p v-if="conversation.last_message" class="last-message">
                <span v-if="conversation.last_message.sender_id === authStore.user?.id" class="message-sender">
                  You: 
                </span>
                <span v-else-if="conversation.type === 'group'" class="message-sender">
                  {{ conversation.last_message.sender_display_name }}: 
                </span>
                {{ truncateMessage(conversation.last_message.content) }}
              </p>
              <p v-else class="no-messages">
                No messages yet
              </p>

              <!-- Unread count -->
              <div 
                v-if="conversation.unread_count > 0"
                class="unread-badge"
              >
                {{ conversation.unread_count > 99 ? '99+' : conversation.unread_count }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.conversation-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.search-container {
  padding: 16px;
  border-bottom: 1px solid var(--sidebar-border);
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-tertiary);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid var(--input-border);
  border-radius: 20px;
  font-size: 0.9rem;
  background: var(--input-background);
  color: var(--input-text);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  background: var(--color-surface-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input::placeholder {
  color: var(--input-placeholder);
}

.new-conversation-button {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.new-conversation-button:hover {
  background: var(--button-primary-hover);
  transform: scale(1.05);
}

.conversations-container {
  flex: 1;
  overflow-y: auto;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--color-text-tertiary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-icon {
  color: var(--color-text-muted);
  margin-bottom: 16px;
}

.empty-text {
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.start-conversation-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.start-conversation-button:hover {
  background: #5a6fd8;
}

.conversations-list {
  padding: 8px 0;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border-left: 3px solid transparent;
}

.conversation-item:hover {
  background: var(--sidebar-item-hover);
}

.conversation-item.active {
  background: var(--sidebar-item-active);
  border-left-color: var(--color-primary-600);
}

.conversation-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  margin-right: 12px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.online { background: #10b981; }
.status-indicator.away { background: #f59e0b; }
.status-indicator.busy { background: #ef4444; }
.status-indicator.offline { background: #6b7280; }

.online-count {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: #10b981;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  border: 2px solid white;
  min-width: 18px;
  text-align: center;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.conversation-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message-time {
  font-size: 0.75rem;
  color: #666;
  flex-shrink: 0;
  margin-left: 8px;
}

.conversation-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message, .no-messages {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.message-sender {
  font-weight: 500;
}

.unread-badge {
  background: #667eea;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  flex-shrink: 0;
  margin-left: 8px;
}
</style>
