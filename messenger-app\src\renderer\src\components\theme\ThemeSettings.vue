<template>
  <div class="theme-settings">
    <h3 class="settings-title">Appearance</h3>
    
    <div class="theme-section">
      <h4 class="section-title">Theme</h4>
      <p class="section-description">Choose your preferred theme or follow your system setting</p>
      
      <div class="theme-options">
        <div
          v-for="mode in themeModes"
          :key="mode"
          class="theme-option"
          :class="{ active: themeMode === mode }"
          @click="setTheme(mode)"
        >
          <div class="theme-preview">
            <div class="preview-header" :class="`preview-${mode}`">
              <div class="preview-dot"></div>
              <div class="preview-dot"></div>
              <div class="preview-dot"></div>
            </div>
            <div class="preview-content" :class="`preview-${mode}`">
              <div class="preview-sidebar" :class="`preview-${mode}`"></div>
              <div class="preview-main" :class="`preview-${mode}`">
                <div class="preview-message own" :class="`preview-${mode}`"></div>
                <div class="preview-message other" :class="`preview-${mode}`"></div>
              </div>
            </div>
          </div>
          
          <div class="theme-info">
            <div class="theme-name">{{ getThemeDisplayName(mode) }}</div>
            <div class="theme-description">{{ getThemeDescription(mode) }}</div>
          </div>
          
          <div class="theme-icon">
            <!-- Light theme icon -->
            <svg v-if="mode === 'light'" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            
            <!-- Dark theme icon -->
            <svg v-else-if="mode === 'dark'" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
            
            <!-- System theme icon -->
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
              <line x1="8" y1="21" x2="16" y2="21"></line>
              <line x1="12" y1="17" x2="12" y2="21"></line>
            </svg>
          </div>
        </div>
      </div>
      
      <div v-if="themeMode === 'system'" class="system-info">
        <div class="system-status">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 6v6l4 2"></path>
          </svg>
          <span>
            System is currently using {{ systemPrefersDark ? 'dark' : 'light' }} mode
          </span>
        </div>
      </div>
    </div>

    <div class="current-theme-info">
      <h4 class="section-title">Current Theme</h4>
      <div class="theme-status">
        <div class="status-item">
          <span class="status-label">Mode:</span>
          <span class="status-value">{{ getCurrentThemeInfo().displayName }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Appearance:</span>
          <span class="status-value">{{ isDark ? 'Dark' : 'Light' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTheme, type ThemeMode } from '../../composables/useTheme'

const { 
  themeMode, 
  isDark, 
  systemPrefersDark, 
  setTheme, 
  getThemeDisplayName, 
  getCurrentThemeInfo,
  themeModes 
} = useTheme()

const getThemeDescription = (mode: ThemeMode): string => {
  switch (mode) {
    case 'light':
      return 'Always use light theme'
    case 'dark':
      return 'Always use dark theme'
    case 'system':
      return 'Follow system preference'
    case 'ocean':
      return 'Deep blue ocean-inspired theme'
    case 'forest':
      return 'Natural green forest theme'
    case 'sunset':
      return 'Warm orange sunset theme'
    case 'purple':
      return 'Professional purple theme'
    default:
      return ''
  }
}
</script>

<style scoped>
.theme-settings {
  padding: 20px;
  max-width: 600px;
}

.settings-title {
  margin: 0 0 24px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.theme-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.section-description {
  margin: 0 0 16px 0;
  font-size: 0.875rem;
  color: var(--color-text-tertiary);
  line-height: 1.5;
}

.theme-options {
  display: grid;
  gap: 12px;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--color-surface-primary);
}

.theme-option:hover {
  border-color: var(--color-border-secondary);
  background: var(--color-surface-hover);
}

.theme-option.active {
  border-color: var(--color-border-accent);
  background: var(--color-primary-50);
}

.theme-dark .theme-option.active {
  background: rgba(99, 102, 241, 0.1);
}

.theme-preview {
  width: 80px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 1px solid var(--color-border-secondary);
  flex-shrink: 0;
}

.preview-header {
  height: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
}

.preview-header.preview-light {
  background: #f9fafb;
}

.preview-header.preview-dark {
  background: #1f2937;
}

.preview-header.preview-system {
  background: linear-gradient(90deg, #f9fafb 50%, #1f2937 50%);
}

.preview-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--color-text-muted);
}

.preview-content {
  height: 44px;
  display: flex;
}

.preview-content.preview-light {
  background: #ffffff;
}

.preview-content.preview-dark {
  background: #111827;
}

.preview-content.preview-system {
  background: linear-gradient(90deg, #ffffff 50%, #111827 50%);
}

.preview-sidebar {
  width: 24px;
  margin: 4px;
  border-radius: 2px;
}

.preview-sidebar.preview-light {
  background: #f3f4f6;
}

.preview-sidebar.preview-dark {
  background: #374151;
}

.preview-sidebar.preview-system {
  background: linear-gradient(180deg, #f3f4f6 50%, #374151 50%);
}

.preview-main {
  flex: 1;
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.preview-message {
  height: 8px;
  border-radius: 4px;
}

.preview-message.own.preview-light {
  background: #6366f1;
  margin-left: 16px;
}

.preview-message.other.preview-light {
  background: #e5e7eb;
  margin-right: 16px;
}

.preview-message.own.preview-dark {
  background: #6366f1;
  margin-left: 16px;
}

.preview-message.other.preview-dark {
  background: #4b5563;
  margin-right: 16px;
}

.preview-message.preview-system {
  background: linear-gradient(90deg, #e5e7eb 50%, #4b5563 50%);
}

.preview-message.own.preview-system {
  background: #6366f1;
  margin-left: 16px;
}

/* Ocean Theme Previews */
.preview-header.preview-ocean {
  background: #0f172a;
}

.preview-content.preview-ocean {
  background: #0f172a;
}

.preview-sidebar.preview-ocean {
  background: #1e293b;
}

.preview-main.preview-ocean {
  background: #0f172a;
}

.preview-message.own.preview-ocean {
  background: #2563eb;
  margin-left: 16px;
}

.preview-message.other.preview-ocean {
  background: #334155;
  margin-right: 16px;
}

/* Forest Theme Previews */
.preview-header.preview-forest {
  background: #0f1419;
}

.preview-content.preview-forest {
  background: #0f1419;
}

.preview-sidebar.preview-forest {
  background: #1a2332;
}

.preview-main.preview-forest {
  background: #0f1419;
}

.preview-message.own.preview-forest {
  background: #16a34a;
  margin-left: 16px;
}

.preview-message.other.preview-forest {
  background: #2d3748;
  margin-right: 16px;
}

/* Sunset Theme Previews */
.preview-header.preview-sunset {
  background: #1c1917;
}

.preview-content.preview-sunset {
  background: #1c1917;
}

.preview-sidebar.preview-sunset {
  background: #292524;
}

.preview-main.preview-sunset {
  background: #1c1917;
}

.preview-message.own.preview-sunset {
  background: #ea580c;
  margin-left: 16px;
}

.preview-message.other.preview-sunset {
  background: #44403c;
  margin-right: 16px;
}

/* Purple Theme Previews */
.preview-header.preview-purple {
  background: #1e1b4b;
}

.preview-content.preview-purple {
  background: #1e1b4b;
}

.preview-sidebar.preview-purple {
  background: #312e81;
}

.preview-main.preview-purple {
  background: #1e1b4b;
}

.preview-message.own.preview-purple {
  background: #9333ea;
  margin-left: 16px;
}

.preview-message.other.preview-purple {
  background: #4338ca;
  margin-right: 16px;
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
}

.theme-description {
  font-size: 0.875rem;
  color: var(--color-text-tertiary);
}

.theme-icon {
  color: var(--color-text-muted);
  flex-shrink: 0;
}

.theme-option.active .theme-icon {
  color: var(--color-text-accent);
}

.system-info {
  margin-top: 12px;
  padding: 12px;
  background: var(--color-surface-secondary);
  border-radius: var(--radius-md);
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.current-theme-info {
  padding-top: 24px;
  border-top: 1px solid var(--color-border-primary);
}

.theme-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 0.875rem;
  color: var(--color-text-tertiary);
}

.status-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
}
</style>
