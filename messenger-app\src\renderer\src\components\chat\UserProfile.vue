<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useAuthStore } from '../../stores/auth'
import ThemeSettings from '../theme/ThemeSettings.vue'

const emit = defineEmits<{
  close: []
}>()

const authStore = useAuthStore()

const isEditing = ref(false)
const isUpdating = ref(false)
const activeTab = ref<'profile' | 'appearance'>('profile')

const profileForm = reactive({
  display_name: authStore.user?.display_name || '',
  email: authStore.user?.email || '',
  status: authStore.user?.status || 'online'
})

const statusOptions = [
  { value: 'online', label: 'Online', color: 'var(--color-text-success)' },
  { value: 'away', label: 'Away', color: 'var(--color-text-warning)' },
  { value: 'busy', label: 'Busy', color: 'var(--color-text-error)' },
  { value: 'offline', label: 'Offline', color: 'var(--color-text-muted)' }
]

const startEditing = () => {
  isEditing.value = true
  profileForm.display_name = authStore.user?.display_name || ''
  profileForm.email = authStore.user?.email || ''
  profileForm.status = authStore.user?.status || 'online'
}

const cancelEditing = () => {
  isEditing.value = false
  profileForm.display_name = authStore.user?.display_name || ''
  profileForm.email = authStore.user?.email || ''
  profileForm.status = authStore.user?.status || 'online'
}

const saveProfile = async () => {
  if (isUpdating.value) return

  isUpdating.value = true

  try {
    const success = await authStore.updateProfile({
      display_name: profileForm.display_name.trim(),
      email: profileForm.email.trim(),
      status: profileForm.status
    })

    if (success) {
      isEditing.value = false
    }
  } catch (error) {
    console.error('Failed to update profile:', error)
  } finally {
    isUpdating.value = false
  }
}

const getUserInitials = (): string => {
  const displayName = authStore.user?.display_name || 'User'
  return displayName.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
}

const getStatusColor = (status: string): string => {
  const statusOption = statusOptions.find(option => option.value === status)
  return statusOption?.color || '#6b7280'
}

const closeModal = () => {
  if (isEditing.value) {
    cancelEditing()
  }
  emit('close')
}
</script>

<template>
  <div class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <!-- Modal Header -->
      <div class="modal-header">
        <h2 class="modal-title">Profile Settings</h2>
        <button class="close-button" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <!-- Tab Navigation -->
      <div class="tab-navigation">
        <button
          class="tab-button"
          :class="{ active: activeTab === 'profile' }"
          @click="activeTab = 'profile'"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
          Profile
        </button>
        <button
          class="tab-button"
          :class="{ active: activeTab === 'appearance' }"
          @click="activeTab = 'appearance'"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="5"/>
            <line x1="12" y1="1" x2="12" y2="3"/>
            <line x1="12" y1="21" x2="12" y2="23"/>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
            <line x1="1" y1="12" x2="3" y2="12"/>
            <line x1="21" y1="12" x2="23" y2="12"/>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
          </svg>
          Appearance
        </button>
      </div>

      <!-- Profile Content -->
      <div class="profile-content">
        <!-- Profile Tab -->
        <div v-if="activeTab === 'profile'" class="tab-content">
          <!-- Avatar Section -->
          <div class="avatar-section">
            <div class="user-avatar">
              <img
                v-if="authStore.user?.avatar_url"
                :src="authStore.user.avatar_url"
                :alt="authStore.user.display_name"
                class="avatar-image"
              />
              <div v-else class="avatar-placeholder">
                {{ getUserInitials() }}
              </div>
            </div>

            <button class="change-avatar-button" disabled>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                <circle cx="12" cy="13" r="4"/>
              </svg>
              Change Photo
            </button>
          </div>

          <!-- Profile Form -->
          <div class="profile-form">
          <div class="form-group">
            <label class="form-label">Username</label>
            <input
              type="text"
              :value="authStore.user?.username"
              class="form-input"
              disabled
              readonly
            />
            <p class="form-help">Username cannot be changed</p>
          </div>

          <div class="form-group">
            <label class="form-label">Display Name</label>
            <input
              v-if="isEditing"
              v-model="profileForm.display_name"
              type="text"
              class="form-input"
              placeholder="Enter your display name"
              :disabled="isUpdating"
            />
            <input
              v-else
              type="text"
              :value="authStore.user?.display_name"
              class="form-input"
              disabled
              readonly
            />
          </div>

          <div class="form-group">
            <label class="form-label">Email</label>
            <input
              v-if="isEditing"
              v-model="profileForm.email"
              type="email"
              class="form-input"
              placeholder="Enter your email"
              :disabled="isUpdating"
            />
            <input
              v-else
              type="email"
              :value="authStore.user?.email"
              class="form-input"
              disabled
              readonly
            />
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <div v-if="isEditing" class="status-selector">
              <div
                v-for="status in statusOptions"
                :key="status.value"
                class="status-option"
                :class="{ 'selected': profileForm.status === status.value }"
                @click="profileForm.status = status.value"
              >
                <div 
                  class="status-indicator"
                  :style="{ backgroundColor: status.color }"
                ></div>
                <span class="status-label">{{ status.label }}</span>
              </div>
            </div>
            <div v-else class="current-status">
              <div 
                class="status-indicator"
                :style="{ backgroundColor: getStatusColor(authStore.user?.status || 'offline') }"
              ></div>
              <span class="status-label">
                {{ statusOptions.find(s => s.value === authStore.user?.status)?.label || 'Offline' }}
              </span>
            </div>
          </div>

          <!-- Account Info -->
          <div class="account-info">
            <div class="info-item">
              <span class="info-label">Member since:</span>
              <span class="info-value">
                {{ authStore.user?.created_at ? new Date(authStore.user.created_at).toLocaleDateString() : 'Unknown' }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">Last seen:</span>
              <span class="info-value">
                {{ authStore.user?.last_seen ? new Date(authStore.user.last_seen).toLocaleString() : 'Unknown' }}
              </span>
            </div>
          </div>
          </div>

          <!-- Modal Actions -->
          <div class="modal-actions">
        <div v-if="isEditing" class="edit-actions">
          <button 
            class="cancel-button"
            @click="cancelEditing"
            :disabled="isUpdating"
          >
            Cancel
          </button>
          <button 
            class="save-button"
            @click="saveProfile"
            :disabled="isUpdating"
          >
            <span v-if="isUpdating" class="loading-spinner"></span>
            {{ isUpdating ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
        
        <div v-else class="view-actions">
          <button class="edit-button" @click="startEditing">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
            Edit Profile
          </button>
          </div>
        </div>
        </div>

        <!-- Appearance Tab -->
        <div v-if="activeTab === 'appearance'" class="tab-content">
          <ThemeSettings />
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-surface-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: var(--color-surface-elevated);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--color-border-primary);
  background: var(--color-surface-secondary);
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 20px;
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--color-text-secondary);
  background: var(--color-surface-hover);
}

.tab-button.active {
  color: var(--color-text-accent);
  background: var(--color-surface-elevated);
  border-bottom-color: var(--color-border-accent);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.close-button:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.profile-content {
  flex: 1;
  overflow-y: auto;
}

.tab-content {
  padding: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.8rem;
}

.change-avatar-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: 1px solid #e1e5e9;
  color: #666;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-avatar-button:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.change-avatar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--color-text-primary);
}

.form-input {
  padding: 12px 16px;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  background: var(--color-surface-primary);
  color: var(--color-text-primary);
  transition: border-color var(--transition-fast);
}

.form-input:focus:not(:disabled) {
  outline: none;
  border-color: var(--color-border-accent);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input:disabled {
  background: var(--color-surface-secondary);
  color: var(--color-text-muted);
  cursor: not-allowed;
}

.form-help {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

.status-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-option:hover {
  border-color: #667eea;
  background: #f8f9fa;
}

.status-option.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.current-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-label {
  font-size: 0.9rem;
  color: #333;
  text-transform: capitalize;
}

.account-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 0.9rem;
  color: #666;
}

.info-value {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
}

.modal-actions {
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
}

.edit-actions, .view-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-button {
  background: none;
  border: 1px solid var(--color-border-primary);
  color: var(--color-text-secondary);
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.cancel-button:hover:not(:disabled) {
  border-color: var(--color-border-secondary);
  color: var(--color-text-primary);
}

.save-button {
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
  border: none;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-button:hover:not(:disabled) {
  background: var(--color-primary-700);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.edit-button {
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
  border: none;
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-button:hover {
  background: var(--color-primary-700);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  margin: 16px 24px 0;
  background: #fee;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  border: 1px solid #fed7d7;
}
</style>
