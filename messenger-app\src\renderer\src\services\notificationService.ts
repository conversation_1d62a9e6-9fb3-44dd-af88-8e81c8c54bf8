interface NotificationOptions {
  title: string
  body: string
  icon?: string
  tag?: string
}

interface NotificationSettings {
  enabled: boolean
  soundEnabled: boolean
  showPreview: boolean
  onlyWhenUnfocused: boolean
}

class NotificationService {
  private settings: NotificationSettings = {
    enabled: true,
    soundEnabled: true,
    showPreview: true,
    onlyWhenUnfocused: true
  }

  private audio: HTMLAudioElement | null = null

  constructor() {
    this.loadSettings()
    this.initializeAudio()
  }

  private loadSettings(): void {
    const saved = localStorage.getItem('notification-settings')
    if (saved) {
      try {
        this.settings = { ...this.settings, ...JSON.parse(saved) }
      } catch (error) {
        console.warn('Failed to load notification settings:', error)
      }
    }
  }

  private saveSettings(): void {
    localStorage.setItem('notification-settings', JSON.stringify(this.settings))
  }

  private initializeAudio(): void {
    // Create audio element for notification sounds
    this.audio = new Audio()
    this.audio.preload = 'auto'
    // We'll add sound files later
  }

  async showNotification(options: NotificationOptions): Promise<boolean> {
    if (!this.settings.enabled) {
      return false
    }

    // Check if we should only show notifications when app is unfocused
    if (this.settings.onlyWhenUnfocused) {
      try {
        const isAppFocused = await window.api.isAppFocused()
        if (isAppFocused) {
          return false
        }
      } catch (error) {
        console.warn('Failed to check app focus:', error)
      }
    }

    // Play notification sound
    if (this.settings.soundEnabled) {
      this.playNotificationSound()
    }

    // Show system notification
    try {
      const result = await window.api.showNotification({
        title: options.title,
        body: this.settings.showPreview ? options.body : 'New message',
        icon: options.icon,
        tag: options.tag
      })
      return result
    } catch (error) {
      console.error('Failed to show notification:', error)
      return false
    }
  }

  private playNotificationSound(): void {
    if (!this.audio || !this.settings.soundEnabled) {
      return
    }

    try {
      // For now, we'll use a simple beep sound
      // Later we can add custom sound files
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.3)
    } catch (error) {
      console.warn('Failed to play notification sound:', error)
    }
  }

  // Notification methods for different types of events
  async showMessageNotification(senderName: string, message: string, conversationId?: number): Promise<boolean> {
    return this.showNotification({
      title: `New message from ${senderName}`,
      body: message,
      tag: `message-${conversationId || 'unknown'}`
    })
  }

  async showCallNotification(callerName: string, type: 'video' | 'audio'): Promise<boolean> {
    return this.showNotification({
      title: `Incoming ${type} call`,
      body: `${callerName} is calling you`,
      tag: `call-${type}`
    })
  }

  async showReactionNotification(senderName: string, emoji: string, messagePreview: string): Promise<boolean> {
    return this.showNotification({
      title: `${senderName} reacted to your message`,
      body: `${emoji} "${messagePreview}"`,
      tag: 'reaction'
    })
  }

  // Settings management
  getSettings(): NotificationSettings {
    return { ...this.settings }
  }

  updateSettings(newSettings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings }
    this.saveSettings()
  }

  // Test notification
  async testNotification(): Promise<boolean> {
    return this.showNotification({
      title: 'Test Notification',
      body: 'This is a test notification from Messenger App',
      tag: 'test'
    })
  }

  // Focus app window
  async focusApp(): Promise<boolean> {
    try {
      return await window.api.focusApp()
    } catch (error) {
      console.error('Failed to focus app:', error)
      return false
    }
  }
}

// Create singleton instance
export const notificationService = new NotificationService()
export type { NotificationSettings }
