import { ElectronAPI } from '@electron-toolkit/preload'

interface NotificationOptions {
  title: string
  body: string
  icon?: string
  tag?: string
}

interface CustomAPI {
  showNotification: (options: NotificationOptions) => Promise<boolean>
  isAppFocused: () => Promise<boolean>
  focusApp: () => Promise<boolean>
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: CustomAPI
  }
}
