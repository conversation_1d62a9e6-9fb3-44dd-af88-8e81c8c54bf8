import { Peer, MediaConnection, DataConnection } from 'peerjs'
import { ref, reactive } from 'vue'

export interface CallState {
  isInCall: boolean
  isInitiating: boolean
  isReceiving: boolean
  localStream: MediaStream | null
  remoteStream: MediaStream | null
  currentCall: MediaConnection | null
  callerId: string | null
  callerName: string | null
  isVideoEnabled: boolean
  isAudioEnabled: boolean
  callStartTime: Date | null
}

export interface WebRTCService {
  peer: Peer | null
  callState: CallState
  initializePeer: (userId: string) => Promise<void>
  initiateCall: (targetUserId: string, targetUserName: string, isVideoCall: boolean) => Promise<void>
  answerCall: (call: MediaConnection) => Promise<void>
  endCall: () => void
  toggleVideo: () => void
  toggleAudio: () => void
  onIncomingCall: (callback: (call: MediaConnection, callerId: string, callerName: string, isVideo: boolean) => void) => void
  cleanup: () => void
}

class WebRTCServiceImpl implements WebRTCService {
  peer: Peer | null = null
  callState: CallState = reactive({
    isInCall: false,
    isInitiating: false,
    isReceiving: false,
    localStream: null,
    remoteStream: null,
    currentCall: null,
    callerId: null,
    callerName: null,
    isVideoEnabled: true,
    isAudioEnabled: true,
    callStartTime: null
  })

  private incomingCallCallback: ((call: MediaConnection, callerId: string, callerName: string, isVideo: boolean) => void) | null = null

  async initializePeer(userId: string): Promise<void> {
    try {
      // Create peer with user ID - using default PeerJS cloud server for now
      this.peer = new Peer(`messenger-${userId}`, {
        debug: 2,
        config: {
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
          ]
        }
      })

      // Handle peer connection events
      this.peer.on('open', (id) => {
        console.log('🔗 PeerJS connected with ID:', id)
      })

      this.peer.on('error', (error) => {
        console.error('❌ PeerJS error:', error)
      })

      // Handle incoming calls
      this.peer.on('call', (call) => {
        console.log('📞 Incoming call from:', call.peer)
        
        // Extract caller info from metadata
        const metadata = call.metadata || {}
        const callerId = metadata.callerId || call.peer.replace('messenger-', '')
        const callerName = metadata.callerName || 'Unknown'
        const isVideo = metadata.isVideo || false

        if (this.incomingCallCallback) {
          this.incomingCallCallback(call, callerId, callerName, isVideo)
        }
      })

    } catch (error) {
      console.error('Failed to initialize PeerJS:', error)
      throw error
    }
  }

  async initiateCall(targetUserId: string, targetUserName: string, isVideoCall: boolean): Promise<void> {
    if (!this.peer) {
      throw new Error('Peer not initialized')
    }

    try {
      this.callState.isInitiating = true
      this.callState.isVideoEnabled = isVideoCall
      this.callState.isAudioEnabled = true

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: isVideoCall,
        audio: true
      })

      this.callState.localStream = stream

      // Call the target user
      const targetPeerId = `messenger-${targetUserId}`
      const call = this.peer.call(targetPeerId, stream, {
        metadata: {
          callerId: this.peer.id?.replace('messenger-', ''),
          callerName: 'You', // This should be the current user's name
          isVideo: isVideoCall
        }
      })

      this.callState.currentCall = call
      this.callState.callerId = targetUserId
      this.callState.callerName = targetUserName

      // Handle remote stream
      call.on('stream', (remoteStream) => {
        console.log('📺 Received remote stream')
        this.callState.remoteStream = remoteStream
        this.callState.isInCall = true
        this.callState.isInitiating = false
        this.callState.callStartTime = new Date()
      })

      // Handle call close
      call.on('close', () => {
        console.log('📞 Call ended')
        this.endCall()
      })

      call.on('error', (error) => {
        console.error('❌ Call error:', error)
        this.endCall()
      })

    } catch (error) {
      console.error('Failed to initiate call:', error)
      this.callState.isInitiating = false
      throw error
    }
  }

  async answerCall(call: MediaConnection): Promise<void> {
    try {
      this.callState.isReceiving = true
      
      // Extract metadata
      const metadata = call.metadata || {}
      const isVideo = metadata.isVideo || false
      
      this.callState.isVideoEnabled = isVideo
      this.callState.isAudioEnabled = true

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: isVideo,
        audio: true
      })

      this.callState.localStream = stream
      this.callState.currentCall = call

      // Answer the call
      call.answer(stream)

      // Handle remote stream
      call.on('stream', (remoteStream) => {
        console.log('📺 Received remote stream')
        this.callState.remoteStream = remoteStream
        this.callState.isInCall = true
        this.callState.isReceiving = false
        this.callState.callStartTime = new Date()
      })

      // Handle call close
      call.on('close', () => {
        console.log('📞 Call ended')
        this.endCall()
      })

      call.on('error', (error) => {
        console.error('❌ Call error:', error)
        this.endCall()
      })

    } catch (error) {
      console.error('Failed to answer call:', error)
      this.callState.isReceiving = false
      throw error
    }
  }

  endCall(): void {
    // Close the call
    if (this.callState.currentCall) {
      this.callState.currentCall.close()
    }

    // Stop local stream
    if (this.callState.localStream) {
      this.callState.localStream.getTracks().forEach(track => track.stop())
    }

    // Reset call state
    this.callState.isInCall = false
    this.callState.isInitiating = false
    this.callState.isReceiving = false
    this.callState.localStream = null
    this.callState.remoteStream = null
    this.callState.currentCall = null
    this.callState.callerId = null
    this.callState.callerName = null
    this.callState.isVideoEnabled = true
    this.callState.isAudioEnabled = true
    this.callState.callStartTime = null
  }

  toggleVideo(): void {
    if (this.callState.localStream) {
      const videoTrack = this.callState.localStream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled
        this.callState.isVideoEnabled = videoTrack.enabled
      }
    }
  }

  toggleAudio(): void {
    if (this.callState.localStream) {
      const audioTrack = this.callState.localStream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled
        this.callState.isAudioEnabled = audioTrack.enabled
      }
    }
  }

  onIncomingCall(callback: (call: MediaConnection, callerId: string, callerName: string, isVideo: boolean) => void): void {
    this.incomingCallCallback = callback
  }

  cleanup(): void {
    this.endCall()
    if (this.peer) {
      this.peer.destroy()
      this.peer = null
    }
  }
}

// Create singleton instance
export const webrtcService: WebRTCService = new WebRTCServiceImpl()
