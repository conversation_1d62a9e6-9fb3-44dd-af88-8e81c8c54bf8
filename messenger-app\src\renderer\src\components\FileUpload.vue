<template>
  <div class="file-upload-container">
    <!-- Drag and Drop Area -->
    <div
      ref="dropZone"
      class="drop-zone"
      :class="{ 'drag-over': isDragOver, 'uploading': isUploading }"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      @click="triggerFileInput"
    >
      <div class="drop-zone-content">
        <div class="upload-icon">
          <svg v-if="!isUploading" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
          <div v-else class="loading-spinner"></div>
        </div>
        <p class="upload-text">
          <span v-if="!isUploading">
            {{ isDragOver ? 'Drop files here' : 'Drag & drop files here or click to browse' }}
          </span>
          <span v-else>Uploading files...</span>
        </p>
        <p class="upload-subtext" v-if="!isUploading">
          Supports images, documents, audio, and video files (max 50MB each)
        </p>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept="image/*,application/pdf,text/plain,.doc,.docx,.zip,audio/*,video/*"
      @change="handleFileSelect"
      style="display: none"
    >

    <!-- Upload Progress -->
    <div v-if="uploadProgress.length > 0" class="upload-progress">
      <div v-for="progress in uploadProgress" :key="progress.id" class="progress-item">
        <div class="progress-info">
          <span class="file-name">{{ progress.name }}</span>
          <span class="progress-percent">{{ Math.round(progress.percent) }}%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress.percent + '%' }"></div>
        </div>
      </div>
    </div>

    <!-- File Preview -->
    <div v-if="uploadedFiles.length > 0" class="file-preview">
      <h4>Uploaded Files</h4>
      <div class="file-grid">
        <div v-for="file in uploadedFiles" :key="file.id" class="file-item">
          <div class="file-thumbnail">
            <img v-if="file.isImage && file.thumbnailUrl" :src="file.thumbnailUrl" :alt="file.originalName">
            <div v-else class="file-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </div>
          </div>
          <div class="file-info">
            <p class="file-name">{{ file.originalName }}</p>
            <p class="file-size">{{ formatFileSize(file.size) }}</p>
          </div>
          <button @click="removeFile(file.id)" class="remove-btn" title="Remove file">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Send Button -->
    <div v-if="uploadedFiles.length > 0" class="send-actions">
      <button @click="sendFiles" class="send-btn" :disabled="isUploading">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="22" y1="2" x2="11" y2="13"></line>
          <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
        </svg>
        Send {{ uploadedFiles.length }} file{{ uploadedFiles.length > 1 ? 's' : '' }}
      </button>
      <button @click="clearFiles" class="clear-btn">Clear All</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useAuthStore } from '../stores/auth';

interface UploadedFile {
  id: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  isImage: boolean;
  uploadedAt: string;
}

interface UploadProgress {
  id: string;
  name: string;
  percent: number;
}

const emit = defineEmits<{
  filesUploaded: [files: UploadedFile[]];
  filesSent: [files: UploadedFile[]];
}>();

const authStore = useAuthStore();

const dropZone = ref<HTMLElement>();
const fileInput = ref<HTMLInputElement>();
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadedFiles = ref<UploadedFile[]>([]);
const uploadProgress = reactive<UploadProgress[]>([]);

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  if (!dropZone.value?.contains(e.relatedTarget as Node)) {
    isDragOver.value = false;
  }
};

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  isDragOver.value = false;
  
  const files = Array.from(e.dataTransfer?.files || []);
  if (files.length > 0) {
    uploadFiles(files);
  }
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  if (files.length > 0) {
    uploadFiles(files);
  }
  target.value = ''; // Reset input
};

const uploadFiles = async (files: File[]) => {
  if (isUploading.value) return;
  
  isUploading.value = true;
  
  try {
    // Initialize progress tracking
    files.forEach(file => {
      uploadProgress.push({
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        percent: 0
      });
    });

    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const response = await fetch('/api/files/upload/multiple', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.success) {
      uploadedFiles.value.push(...result.files);
      emit('filesUploaded', result.files);
    } else {
      throw new Error(result.error || 'Upload failed');
    }
  } catch (error) {
    console.error('File upload error:', error);
    alert(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    isUploading.value = false;
    uploadProgress.splice(0); // Clear progress
  }
};

const removeFile = async (fileId: string) => {
  const fileIndex = uploadedFiles.value.findIndex(f => f.id === fileId);
  if (fileIndex === -1) return;

  const file = uploadedFiles.value[fileIndex];
  
  try {
    const response = await fetch(`/api/files/${file.filename}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (response.ok) {
      uploadedFiles.value.splice(fileIndex, 1);
    } else {
      console.error('Failed to delete file from server');
    }
  } catch (error) {
    console.error('Error deleting file:', error);
  }
};

const sendFiles = () => {
  if (uploadedFiles.value.length > 0) {
    emit('filesSent', [...uploadedFiles.value]);
    clearFiles();
  }
};

const clearFiles = () => {
  uploadedFiles.value.forEach(file => {
    removeFile(file.id);
  });
  uploadedFiles.value = [];
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style scoped>
.file-upload-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.drop-zone {
  border: 2px dashed #e1e5e9;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.drop-zone:hover,
.drop-zone.drag-over {
  border-color: #4f46e5;
  background: #f0f9ff;
}

.drop-zone.uploading {
  border-color: #10b981;
  background: #f0fdf4;
  cursor: not-allowed;
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  color: #6b7280;
  transition: color 0.3s ease;
}

.drop-zone:hover .upload-icon,
.drop-zone.drag-over .upload-icon {
  color: #4f46e5;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.upload-subtext {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.upload-progress {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.progress-item {
  margin-bottom: 12px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.file-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.progress-percent {
  font-size: 12px;
  color: #6b7280;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.file-preview {
  margin-top: 20px;
}

.file-preview h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  position: relative;
}

.file-thumbnail {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e5e7eb;
}

.file-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  color: #6b7280;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-info .file-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.remove-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-btn:hover {
  background: #dc2626;
}

.send-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.send-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #4338ca;
}

.send-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.clear-btn {
  padding: 12px 24px;
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #f3f4f6;
  color: #374151;
}
</style>
