<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Dark Login | Vue.js</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    body {
      font-family: 'Inter', sans-serif;
    }
    
    .gradient-bg {
      background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    }
    
    .glass-effect {
      background: rgba(30, 41, 59, 0.7);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(148, 163, 184, 0.1);
    }
    
    .input-focus:focus {
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }
    
    .btn-gradient {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      transition: all 0.3s ease;
    }
    
    .btn-gradient:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
    }
    
    .fade-enter-active, .fade-leave-active {
      transition: opacity 0.5s;
    }
    
    .fade-enter-from, .fade-leave-to {
      opacity: 0;
    }
    
    .shake {
      animation: shake 0.5s;
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
      20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
  </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
  <div id="app">
    <div class="w-full max-w-md">
      <!-- Logo and Title -->
      <div class="text-center mb-10">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-500 mb-4">
          <i class="fas fa-rocket text-white text-2xl"></i>
        </div>
        <h1 class="text-3xl font-bold text-white">Welcome Back</h1>
        <p class="text-slate-400 mt-2">Sign in to continue to your account</p>
      </div>
      
      <!-- Login Form -->
      <div class="glass-effect rounded-2xl p-8 shadow-xl">
        <form @submit.prevent="handleLogin">
          <!-- Email Field -->
          <div class="mb-5">
            <label class="block text-slate-300 text-sm font-medium mb-2" for="email">
              Email Address
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-envelope text-slate-500"></i>
              </div>
              <input
                id="email"
                v-model="email"
                type="email"
                class="input-focus w-full pl-10 pr-3 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                :class="{'border-red-500': errors.email}"
                placeholder="<EMAIL>"
              />
            </div>
            <transition name="fade">
              <p v-if="errors.email" class="mt-1 text-red-400 text-sm">{{ errors.email }}</p>
            </transition>
          </div>
          
          <!-- Password Field -->
          <div class="mb-6">
            <label class="block text-slate-300 text-sm font-medium mb-2" for="password">
              Password
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-lock text-slate-500"></i>
              </div>
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                class="input-focus w-full pl-10 pr-10 py-3 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                :class="{'border-red-500': errors.password}"
                placeholder="••••••••"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-slate-500 hover:text-slate-300"></i>
              </button>
            </div>
            <transition name="fade">
              <p v-if="errors.password" class="mt-1 text-red-400 text-sm">{{ errors.password }}</p>
            </transition>
          </div>
          
          <!-- Remember Me & Forgot Password -->
          <div class="flex items-center justify-between mb-6">
            <label class="flex items-center">
              <input
                v-model="rememberMe"
                type="checkbox"
                class="h-4 w-4 rounded border-slate-700 bg-slate-800 text-indigo-500 focus:ring-indigo-500 focus:ring-offset-0"
              />
              <span class="ml-2 text-sm text-slate-400">Remember me</span>
            </label>
            <a href="#" class="text-sm font-medium text-indigo-400 hover:text-indigo-300 transition">
              Forgot password?
            </a>
          </div>
          
          <!-- Submit Button -->
          <button
            type="submit"
            :disabled="isLoading"
            class="btn-gradient w-full py-3 px-4 rounded-lg text-white font-medium flex items-center justify-center"
            :class="{'opacity-70 cursor-not-allowed': isLoading}"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Signing In...' : 'Sign In' }}
          </button>
        </form>
        
        <!-- Sign Up Link -->
        <p class="mt-8 text-center text-sm text-slate-400">
          Don't have an account?
          <a href="#" class="font-medium text-indigo-400 hover:text-indigo-300 transition">
            Sign up
          </a>
        </p>
      </div>
      
      <!-- Success/Error Messages -->
      <transition name="fade">
        <div v-if="message" class="mt-4 p-4 rounded-lg text-center" :class="messageType === 'success' ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'">
          <i :class="messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'" class="mr-2"></i>
          {{ message }}
        </div>
      </transition>
    </div>
  </div>

  <script>
    const { createApp, ref, reactive } = Vue;
    
    createApp({
      setup() {
        const email = ref('');
        const password = ref('');
        const rememberMe = ref(false);
        const showPassword = ref(false);
        const isLoading = ref(false);
        const message = ref('');
        const messageType = ref('');
        
        const errors = reactive({
          email: '',
          password: ''
        });
        
        const validateForm = () => {
          let isValid = true;
          
          // Reset errors
          errors.email = '';
          errors.password = '';
          
          // Email validation
          if (!email.value) {
            errors.email = 'Email is required';
            isValid = false;
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
            errors.email = 'Please enter a valid email';
            isValid = false;
          }
          
          // Password validation
          if (!password.value) {
            errors.password = 'Password is required';
            isValid = false;
          } else if (password.value.length < 6) {
            errors.password = 'Password must be at least 6 characters';
            isValid = false;
          }
          
          return isValid;
        };
        
        const handleLogin = async () => {
          if (!validateForm()) {
            // Add shake animation to form
            const form = document.querySelector('form');
            form.classList.add('shake');
            setTimeout(() => form.classList.remove('shake'), 500);
            return;
          }
          
          isLoading.value = true;
          message.value = '';
          
          try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Simulate successful login
            if (email.value === '<EMAIL>' && password.value === 'password') {
              message.value = 'Login successful! Redirecting...';
              messageType.value = 'success';
              
              // Simulate redirect
              setTimeout(() => {
                message.value = 'Welcome to your dashboard!';
              }, 2000);
            } else {
              message.value = 'Invalid email or password';
              messageType.value = 'error';
            }
          } catch (error) {
            message.value = 'An error occurred. Please try again.';
            messageType.value = 'error';
          } finally {
            isLoading.value = false;
          }
        };
        
        return {
          email,
          password,
          rememberMe,
          showPassword,
          isLoading,
          message,
          messageType,
          errors,
          handleLogin
        };
      }
    }).mount('#app');
  </script>
</body>
</html>