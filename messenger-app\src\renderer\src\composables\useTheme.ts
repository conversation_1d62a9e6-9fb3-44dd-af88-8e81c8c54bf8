import { ref, computed, watch, onMounted } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'system' | 'ocean' | 'forest' | 'sunset' | 'purple'

interface ThemeState {
  mode: ThemeMode
  isDark: boolean
}

// Global theme state
const themeMode = ref<ThemeMode>('system')
const systemPrefersDark = ref(false)

// Computed property for actual dark mode state
const isDark = computed(() => {
  if (themeMode.value === 'system') {
    return systemPrefersDark.value
  }
  // All custom themes are considered dark themes for system integration
  return ['dark', 'ocean', 'forest', 'sunset', 'purple'].includes(themeMode.value)
})

// Theme persistence key
const THEME_STORAGE_KEY = 'messenger-theme'

export function useTheme() {
  // Load saved theme preference
  const loadTheme = (): void => {
    try {
      const saved = localStorage.getItem(THEME_STORAGE_KEY)
      if (saved && ['light', 'dark', 'system'].includes(saved)) {
        themeMode.value = saved as ThemeMode
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error)
    }
  }

  // Save theme preference
  const saveTheme = (): void => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, themeMode.value)
    } catch (error) {
      console.warn('Failed to save theme preference:', error)
    }
  }

  // Detect system theme preference
  const detectSystemTheme = (): void => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemPrefersDark.value = mediaQuery.matches

      // Listen for system theme changes
      mediaQuery.addEventListener('change', (e) => {
        systemPrefersDark.value = e.matches
      })
    }
  }

  // Apply theme to document
  const applyTheme = (): void => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      
      // Remove existing theme classes
      root.classList.remove('theme-light', 'theme-dark')
      
      // Add current theme class
      if (isDark.value) {
        root.classList.add('theme-dark')
        root.setAttribute('data-theme', 'dark')
      } else {
        root.classList.add('theme-light')
        root.setAttribute('data-theme', 'light')
      }
    }
  }

  // Set theme mode
  const setTheme = (mode: ThemeMode): void => {
    themeMode.value = mode
    saveTheme()
  }

  // Toggle between light and dark (skips system)
  const toggleTheme = (): void => {
    if (themeMode.value === 'light') {
      setTheme('dark')
    } else if (themeMode.value === 'dark') {
      setTheme('light')
    } else {
      // If system, toggle to opposite of current system preference
      setTheme(systemPrefersDark.value ? 'light' : 'dark')
    }
  }

  // Get theme display name
  const getThemeDisplayName = (mode: ThemeMode): string => {
    switch (mode) {
      case 'light':
        return 'Light'
      case 'dark':
        return 'Dark'
      case 'system':
        return 'System'
      case 'ocean':
        return 'Ocean Blue'
      case 'forest':
        return 'Forest Green'
      case 'sunset':
        return 'Sunset Orange'
      case 'purple':
        return 'Purple Pro'
      default:
        return 'Unknown'
    }
  }

  // Get current theme info
  const getCurrentThemeInfo = () => {
    return {
      mode: themeMode.value,
      isDark: isDark.value,
      displayName: getThemeDisplayName(themeMode.value),
      systemPrefersDark: systemPrefersDark.value
    }
  }

  // Initialize theme system
  const initializeTheme = (): void => {
    detectSystemTheme()
    loadTheme()
    applyTheme()
  }

  // Watch for theme changes and apply them
  watch(isDark, applyTheme, { immediate: false })
  watch(themeMode, applyTheme, { immediate: false })

  return {
    // State
    themeMode: computed(() => themeMode.value),
    isDark: computed(() => isDark.value),
    systemPrefersDark: computed(() => systemPrefersDark.value),
    
    // Actions
    setTheme,
    toggleTheme,
    initializeTheme,
    
    // Utilities
    getThemeDisplayName,
    getCurrentThemeInfo,
    
    // Theme modes for selection
    themeModes: ['light', 'dark', 'system', 'ocean', 'forest', 'sunset', 'purple'] as const
  }
}

// Auto-initialize theme system when module is imported
if (typeof window !== 'undefined') {
  const { initializeTheme } = useTheme()
  
  // Initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeTheme)
  } else {
    initializeTheme()
  }
}
