import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

// File upload configuration
const UPLOAD_DIR = path.join(process.cwd(), 'uploads');
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const ALLOWED_FILE_TYPES = [
  ...ALLOWED_IMAGE_TYPES,
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/zip',
  'audio/mpeg',
  'audio/wav',
  'video/mp4',
  'video/webm'
];

// Ensure upload directory exists
export async function ensureUploadDir(): Promise<void> {
  try {
    await fs.access(UPLOAD_DIR);
  } catch {
    await fs.mkdir(UPLOAD_DIR, { recursive: true });
    await fs.mkdir(path.join(UPLOAD_DIR, 'images'), { recursive: true });
    await fs.mkdir(path.join(UPLOAD_DIR, 'files'), { recursive: true });
    await fs.mkdir(path.join(UPLOAD_DIR, 'thumbnails'), { recursive: true });
  }
}

// Multer configuration
const storage = multer.diskStorage({
  destination: async (_req, file, cb) => {
    await ensureUploadDir();
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.mimetype);
    const subDir = isImage ? 'images' : 'files';
    cb(null, path.join(UPLOAD_DIR, subDir));
  },
  filename: (_req, file, cb) => {
    const uniqueId = uuidv4();
    const ext = path.extname(file.originalname);
    cb(null, `${uniqueId}${ext}`);
  }
});

const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (ALLOWED_FILE_TYPES.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} not allowed`));
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: MAX_FILE_SIZE
  }
});

// Generate thumbnail for images
export async function generateThumbnail(filePath: string, filename: string): Promise<string | null> {
  try {
    const thumbnailDir = path.join(UPLOAD_DIR, 'thumbnails');
    const thumbnailPath = path.join(thumbnailDir, `thumb_${filename}`);
    
    await sharp(filePath)
      .resize(200, 200, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);
    
    return `thumbnails/thumb_${filename}`;
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    return null;
  }
}

// File upload handler
export async function handleFileUpload(req: Request, res: Response): Promise<void> {
  try {
    if (!req.file) {
      res.status(400).json({ error: 'No file uploaded' });
      return;
    }

    const file = req.file;
    const isImage = ALLOWED_IMAGE_TYPES.includes(file.mimetype);
    
    // Generate thumbnail for images
    let thumbnailPath: string | null = null;
    if (isImage) {
      const filenameWithoutExt = path.parse(file.filename).name;
      thumbnailPath = await generateThumbnail(file.path, `${filenameWithoutExt}.jpg`);
    }

    // File metadata
    const fileData = {
      id: uuidv4(),
      originalName: file.originalname,
      filename: file.filename,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path,
      url: `/uploads/${isImage ? 'images' : 'files'}/${file.filename}`,
      thumbnailUrl: thumbnailPath ? `/uploads/${thumbnailPath}` : null,
      isImage,
      uploadedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      file: fileData
    });
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({ 
      error: 'File upload failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Multiple file upload handler
export async function handleMultipleFileUpload(req: Request, res: Response): Promise<void> {
  try {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      res.status(400).json({ error: 'No files uploaded' });
      return;
    }

    const fileDataArray = await Promise.all(
      files.map(async (file) => {
        const isImage = ALLOWED_IMAGE_TYPES.includes(file.mimetype);
        
        // Generate thumbnail for images
        let thumbnailPath: string | null = null;
        if (isImage) {
          const filenameWithoutExt = path.parse(file.filename).name;
          thumbnailPath = await generateThumbnail(file.path, `${filenameWithoutExt}.jpg`);
        }

        return {
          id: uuidv4(),
          originalName: file.originalname,
          filename: file.filename,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          url: `/uploads/${isImage ? 'images' : 'files'}/${file.filename}`,
          thumbnailUrl: thumbnailPath ? `/uploads/${thumbnailPath}` : null,
          isImage,
          uploadedAt: new Date().toISOString()
        };
      })
    );

    res.json({
      success: true,
      files: fileDataArray
    });
  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({ 
      error: 'File upload failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Delete file handler
export async function handleFileDelete(req: Request, res: Response): Promise<void> {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      res.status(400).json({ error: 'Filename required' });
      return;
    }

    // Try to delete from both directories
    const imagePath = path.join(UPLOAD_DIR, 'images', filename);
    const filePath = path.join(UPLOAD_DIR, 'files', filename);
    const thumbnailPath = path.join(UPLOAD_DIR, 'thumbnails', `thumb_${path.parse(filename).name}.jpg`);

    try {
      await fs.unlink(imagePath);
    } catch {
      try {
        await fs.unlink(filePath);
      } catch {
        res.status(404).json({ error: 'File not found' });
        return;
      }
    }

    // Try to delete thumbnail if it exists
    try {
      await fs.unlink(thumbnailPath);
    } catch {
      // Thumbnail might not exist, ignore error
    }

    res.json({ success: true, message: 'File deleted successfully' });
  } catch (error) {
    console.error('File delete error:', error);
    res.status(500).json({ 
      error: 'File deletion failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Get file info handler
export async function handleFileInfo(req: Request, res: Response): Promise<void> {
  try {
    const { filename } = req.params;
    
    if (!filename) {
      res.status(400).json({ error: 'Filename required' });
      return;
    }

    // Check both directories
    const imagePath = path.join(UPLOAD_DIR, 'images', filename);
    const filePath = path.join(UPLOAD_DIR, 'files', filename);
    
    let fileStats;
    let isImage = false;

    try {
      fileStats = await fs.stat(imagePath);
      isImage = true;
    } catch {
      try {
        fileStats = await fs.stat(filePath);
      } catch {
        res.status(404).json({ error: 'File not found' });
        return;
      }
    }

    const fileInfo = {
      filename,
      size: fileStats.size,
      isImage,
      url: `/uploads/${isImage ? 'images' : 'files'}/${filename}`,
      createdAt: fileStats.birthtime.toISOString(),
      modifiedAt: fileStats.mtime.toISOString()
    };

    res.json(fileInfo);
  } catch (error) {
    console.error('File info error:', error);
    res.status(500).json({ 
      error: 'Failed to get file info',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
