<template>
  <div v-if="callState.isInCall || callState.isInitiating || callState.isReceiving" class="video-call-overlay">
    <div class="video-call-container">
      <!-- Remote Video -->
      <div class="remote-video-container">
        <video
          ref="remoteVideo"
          class="remote-video"
          autoplay
          playsinline
        ></video>
        
        <!-- Call Info -->
        <div class="call-info">
          <div class="caller-name">{{ callState.callerName || 'Unknown' }}</div>
          <div class="call-status">
            <span v-if="callState.isInitiating">Calling...</span>
            <span v-else-if="callState.isReceiving">Incoming call...</span>
            <span v-else-if="callState.isInCall && callDuration">{{ callDuration }}</span>
          </div>
        </div>
      </div>

      <!-- Local Video (Picture-in-Picture) -->
      <div class="local-video-container" v-if="callState.isVideoEnabled">
        <video
          ref="localVideo"
          class="local-video"
          autoplay
          playsinline
          muted
        ></video>
      </div>

      <!-- Call Controls -->
      <div class="call-controls">
        <!-- Answer/Decline for incoming calls -->
        <div v-if="callState.isReceiving" class="incoming-call-controls">
          <button @click="answerCall" class="control-btn answer-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
            </svg>
          </button>
          <button @click="declineCall" class="control-btn decline-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
            </svg>
          </button>
        </div>

        <!-- Active call controls -->
        <div v-else class="active-call-controls">
          <button 
            @click="toggleAudio" 
            :class="['control-btn', { 'muted': !callState.isAudioEnabled }]"
          >
            <svg v-if="callState.isAudioEnabled" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
            </svg>
            <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73 4.27 3z"/>
            </svg>
          </button>

          <button 
            v-if="callState.isVideoEnabled || !callState.isVideoEnabled"
            @click="toggleVideo" 
            :class="['control-btn', { 'disabled': !callState.isVideoEnabled }]"
          >
            <svg v-if="callState.isVideoEnabled" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
            </svg>
            <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 6.5l-4 4V7c0-.55-.45-1-1-1H9.82l-3.28-3.28c.46-.42 1.06-.72 1.71-.72H16c.55 0 1 .45 1 1v3.5l4-4v11zM3.27 2L2 3.27 4.73 6H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.21 0 .39-.08.55-.18L19.73 21 21 19.73 3.27 2z"/>
            </svg>
          </button>

          <button @click="endCall" class="control-btn end-call-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { webrtcService } from '../../services/webrtcService'
import type { MediaConnection } from 'peerjs'

const remoteVideo = ref<HTMLVideoElement>()
const localVideo = ref<HTMLVideoElement>()

const callState = webrtcService.callState

// Computed property for call duration
const callDuration = computed(() => {
  if (!callState.callStartTime) return ''
  
  const now = new Date()
  const diff = Math.floor((now.getTime() - callState.callStartTime.getTime()) / 1000)
  const minutes = Math.floor(diff / 60)
  const seconds = diff % 60
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// Watch for remote stream changes
watch(() => callState.remoteStream, (newStream) => {
  if (newStream && remoteVideo.value) {
    remoteVideo.value.srcObject = newStream
  }
}, { immediate: true })

// Watch for local stream changes
watch(() => callState.localStream, (newStream) => {
  if (newStream && localVideo.value) {
    localVideo.value.srcObject = newStream
  }
}, { immediate: true })

const answerCall = async () => {
  if (callState.currentCall) {
    try {
      await webrtcService.answerCall(callState.currentCall)
    } catch (error) {
      console.error('Failed to answer call:', error)
    }
  }
}

const declineCall = () => {
  webrtcService.endCall()
}

const endCall = () => {
  webrtcService.endCall()
}

const toggleAudio = () => {
  webrtcService.toggleAudio()
}

const toggleVideo = () => {
  webrtcService.toggleVideo()
}

onMounted(() => {
  // Set up video elements when streams are available
  nextTick(() => {
    if (callState.remoteStream && remoteVideo.value) {
      remoteVideo.value.srcObject = callState.remoteStream
    }
    if (callState.localStream && localVideo.value) {
      localVideo.value.srcObject = callState.localStream
    }
  })
})

onUnmounted(() => {
  // Cleanup is handled by the service
})
</script>

<style scoped>
.video-call-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-call-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.remote-video-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-info {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  z-index: 10;
}

.caller-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.call-status {
  font-size: 1rem;
  opacity: 0.8;
}

.local-video-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
  z-index: 10;
}

.local-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-controls {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.incoming-call-controls,
.active-call-controls {
  display: flex;
  gap: 20px;
  align-items: center;
}

.control-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.control-btn:hover {
  transform: scale(1.1);
}

.answer-btn {
  background: #22c55e;
}

.answer-btn:hover {
  background: #16a34a;
}

.decline-btn,
.end-call-btn {
  background: #ef4444;
}

.decline-btn:hover,
.end-call-btn:hover {
  background: #dc2626;
}

.control-btn.muted,
.control-btn.disabled {
  background: #6b7280;
}

.control-btn:not(.answer-btn):not(.decline-btn):not(.end-call-btn) {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn:not(.answer-btn):not(.decline-btn):not(.end-call-btn):hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
