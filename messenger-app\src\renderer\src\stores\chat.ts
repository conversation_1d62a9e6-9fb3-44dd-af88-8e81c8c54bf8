import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useAuthStore } from './auth'
import { notificationService } from '../services/notificationService'

export interface MessageAttachment {
  id: string
  originalName: string
  filename: string
  mimetype: string
  size: number
  url: string
  thumbnailUrl?: string
  isImage: boolean
  uploadedAt: string
}

export interface MessageReaction {
  emoji: string
  count: number
  users: string[]
  user_ids: number[]
}

export interface Message {
  id: number
  conversation_id: number
  sender: {
    id: number
    username: string
    display_name: string
    avatar_url?: string
    status: string
  }
  content: string
  message_type: 'text' | 'image' | 'file' | 'audio' | 'video'
  file_url?: string
  file_name?: string
  file_size?: number
  attachments?: MessageAttachment[]
  reactions?: MessageReaction[]
  reply_to_id?: number
  reply_to_message?: {
    id: number
    content: string
    sender_username: string
    sender_display_name: string
  }
  edited_at?: string
  created_at: string
}

export interface Conversation {
  id: number
  type: 'direct' | 'group'
  name?: string
  description?: string
  avatar_url?: string
  participants: Array<{
    id: number
    username: string
    display_name: string
    avatar_url?: string
    status: string
    role?: 'admin' | 'member'
    joined_at?: string
  }>
  last_message?: {
    id: number
    content: string
    message_type: string
    sender_id: number
    sender_username: string
    sender_display_name: string
    created_at: string
  }
  unread_count: number
  created_at: string
  updated_at: string
}

export interface Friend {
  id: number
  username: string
  display_name: string
  avatar_url?: string
  status: string
  last_seen: string
  friendship_created_at: string
}

export interface Call {
  id: number
  conversation_id: number
  caller: {
    id: number
    username: string
    display_name: string
    avatar_url?: string
  }
  call_type: 'audio' | 'video'
  status: 'initiated' | 'ringing' | 'answered' | 'ended' | 'declined'
  offer?: RTCSessionDescriptionInit
  answer?: RTCSessionDescriptionInit
}

export const useChatStore = defineStore('chat', () => {
  // State
  const conversations = ref<Conversation[]>([])
  const messages = ref<Map<number, Message[]>>(new Map())
  const friends = ref<Friend[]>([])

  const activeConversation = ref<Conversation | null>(null)
  const typingUsers = ref<Map<number, Set<number>>>(new Map())
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Modal states
  const showNewConversationModal = ref(false)
  const showCallModal = ref(false)
  const activeCall = ref<Call | null>(null)

  // Reply state
  const replyToMessage = ref<Message | null>(null)

  // API Base URL
  const API_BASE = 'http://localhost:3001/api'

  // Computed
  const sortedConversations = computed(() => {
    return [...conversations.value].sort((a, b) => {
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    })
  })

  const activeConversationMessages = computed(() => {
    if (!activeConversation.value) return []
    return messages.value.get(activeConversation.value.id) || []
  })

  const getTypingUsersForConversation = computed(() => {
    return (conversationId: number) => {
      const typingSet = typingUsers.value.get(conversationId)
      if (!typingSet || typingSet.size === 0) return []
      
      const authStore = useAuthStore()
      return Array.from(typingSet).filter(userId => userId !== authStore.user?.id)
    }
  })

  // Actions
  const loadConversations = async (): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token) return

    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/conversations`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load conversations')
      }

      if (data.success) {
        conversations.value = data.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load conversations'
      console.error('Load conversations error:', err)
    } finally {
      isLoading.value = false
    }
  }

  const loadMessages = async (conversationId: number, page = 1): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token) return

    try {
      const response = await fetch(`${API_BASE}/messages/conversation/${conversationId}?page=${page}&limit=50`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load messages')
      }

      if (data.success) {
        const existingMessages = messages.value.get(conversationId) || []
        const newMessages = page === 1 ? data.data : [...data.data, ...existingMessages]
        messages.value.set(conversationId, newMessages)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load messages'
      console.error('Load messages error:', err)
    }
  }

  const sendMessage = async (conversationId: number, content: string, replyToId?: number): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token || !authStore.socket) return

    try {
      // Send via socket for real-time delivery
      authStore.socket.emit('send_message', {
        conversation_id: conversationId,
        content,
        message_type: 'text',
        reply_to_id: replyToId
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to send message'
      console.error('Send message error:', err)
    }
  }

  const sendMessageWithAttachments = async (
    conversationId: number,
    content: string,
    attachments: MessageAttachment[],
    replyToId?: number
  ): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token || !authStore.socket) return

    try {
      // Determine message type based on attachments
      let messageType: 'text' | 'image' | 'file' | 'audio' | 'video' = 'text'
      if (attachments.length > 0) {
        const firstAttachment = attachments[0]
        if (firstAttachment.isImage) {
          messageType = 'image'
        } else if (firstAttachment.mimetype.startsWith('audio/')) {
          messageType = 'audio'
        } else if (firstAttachment.mimetype.startsWith('video/')) {
          messageType = 'video'
        } else {
          messageType = 'file'
        }
      }

      // Send via socket for real-time delivery
      authStore.socket.emit('send_message', {
        conversation_id: conversationId,
        content: content || (attachments.length > 0 ? `Sent ${attachments.length} file${attachments.length > 1 ? 's' : ''}` : ''),
        message_type: messageType,
        attachments,
        reply_to_id: replyToId
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to send message with attachments'
      console.error('Send message with attachments error:', err)
    }
  }

  const createConversation = async (type: 'direct' | 'group', participantIds: number[], name?: string, description?: string): Promise<Conversation | null> => {
    const authStore = useAuthStore()
    if (!authStore.token) return null

    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/conversations`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          participant_ids: participantIds,
          name,
          description
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create conversation')
      }

      if (data.success) {
        const newConversation = data.data
        conversations.value.unshift(newConversation)
        return newConversation
      }

      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create conversation'
      console.error('Create conversation error:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  const loadFriends = async (): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token) return

    try {
      const response = await fetch(`${API_BASE}/users/friends`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load friends')
      }

      if (data.success) {
        friends.value = data.data
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load friends'
      console.error('Load friends error:', err)
    }
  }

  const sendFriendRequest = async (username: string): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token) throw new Error('Not authenticated')

    try {
      const response = await fetch(`${API_BASE}/friendships/request`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send friend request')
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to send friend request')
      }
    } catch (err) {
      console.error('Send friend request error:', err)
      throw err
    }
  }



  const setActiveConversation = async (conversation: Conversation): Promise<void> => {
    activeConversation.value = conversation
    
    // Load messages if not already loaded
    if (!messages.value.has(conversation.id)) {
      await loadMessages(conversation.id)
    }

    // Join conversation room via socket
    const authStore = useAuthStore()
    if (authStore.socket) {
      authStore.socket.emit('join_conversation', { conversation_id: conversation.id })
    }

    // Mark messages as read
    await markMessagesAsRead(conversation.id)
  }

  const markMessagesAsRead = async (conversationId: number): Promise<void> => {
    const authStore = useAuthStore()
    if (!authStore.token) return

    const conversationMessages = messages.value.get(conversationId) || []
    const unreadMessageIds = conversationMessages
      .filter(msg => msg.sender.id !== authStore.user?.id)
      .map(msg => msg.id)

    if (unreadMessageIds.length === 0) return

    try {
      await fetch(`${API_BASE}/messages/read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message_ids: unreadMessageIds }),
      })

      // Update local conversation unread count
      const conversation = conversations.value.find(c => c.id === conversationId)
      if (conversation) {
        conversation.unread_count = 0
      }
    } catch (err) {
      console.error('Mark messages as read error:', err)
    }
  }

  const handleNewMessage = (message: Message): void => {
    // Add message to the conversation
    const conversationMessages = messages.value.get(message.conversation_id) || []
    conversationMessages.push(message)
    messages.value.set(message.conversation_id, conversationMessages)

    // Update conversation's last message and timestamp
    const conversation = conversations.value.find(c => c.id === message.conversation_id)
    if (conversation) {
      conversation.last_message = {
        id: message.id,
        content: message.content,
        message_type: message.message_type,
        sender_id: message.sender.id,
        sender_username: message.sender.username,
        sender_display_name: message.sender.display_name,
        created_at: message.created_at
      }
      conversation.updated_at = message.created_at

      // Increment unread count if not the active conversation or sender is not current user
      const authStore = useAuthStore()
      if (message.sender.id !== authStore.user?.id &&
          (!activeConversation.value || activeConversation.value.id !== message.conversation_id)) {
        conversation.unread_count++

        // Show notification for new message from other users
        notificationService.showMessageNotification(
          message.sender.display_name,
          message.content || 'Sent an attachment',
          message.conversation_id
        ).catch(error => {
          console.warn('Failed to show message notification:', error)
        })
      }
    }

    // Move conversation to top
    const conversationIndex = conversations.value.findIndex(c => c.id === message.conversation_id)
    if (conversationIndex > 0) {
      const [conversation] = conversations.value.splice(conversationIndex, 1)
      conversations.value.unshift(conversation)
    }
  }

  const updateUserStatus = (user: { id: number; status: string }): void => {
    // Update user status in conversations
    conversations.value.forEach(conversation => {
      const participant = conversation.participants.find(p => p.id === user.id)
      if (participant) {
        participant.status = user.status
      }
    })

    // Update user status in friends
    const friend = friends.value.find(f => f.id === user.id)
    if (friend) {
      friend.status = user.status
    }
  }

  const setUserTyping = (conversationId: number, userId: number, isTyping: boolean): void => {
    if (!typingUsers.value.has(conversationId)) {
      typingUsers.value.set(conversationId, new Set())
    }

    const typingSet = typingUsers.value.get(conversationId)!
    
    if (isTyping) {
      typingSet.add(userId)
    } else {
      typingSet.delete(userId)
    }
  }

  const startTyping = (conversationId: number): void => {
    const authStore = useAuthStore()
    if (authStore.socket) {
      authStore.socket.emit('typing_start', { conversation_id: conversationId })
    }
  }

  const stopTyping = (conversationId: number): void => {
    const authStore = useAuthStore()
    if (authStore.socket) {
      authStore.socket.emit('typing_stop', { conversation_id: conversationId })
    }
  }

  // Call handling methods
  const handleIncomingCall = (callData: Call): void => {
    activeCall.value = callData
    showCallModal.value = true

    // Show notification for incoming call
    const callerName = callData.caller_name || 'Unknown'
    const callType = callData.type === 'video' ? 'video' : 'audio'

    notificationService.showCallNotification(callerName, callType).catch(error => {
      console.warn('Failed to show call notification:', error)
    })
  }

  const handleCallAnswered = (data: { call_id: number; answer: RTCSessionDescriptionInit }): void => {
    if (activeCall.value && activeCall.value.id === data.call_id) {
      activeCall.value.answer = data.answer
      activeCall.value.status = 'answered'
    }
  }

  const handleCallDeclined = (data: { call_id: number }): void => {
    if (activeCall.value && activeCall.value.id === data.call_id) {
      activeCall.value.status = 'declined'
      showCallModal.value = false
      activeCall.value = null
    }
  }

  const handleCallEnded = (data: { call_id: number }): void => {
    if (activeCall.value && activeCall.value.id === data.call_id) {
      activeCall.value.status = 'ended'
      showCallModal.value = false
      activeCall.value = null
    }
  }

  const addReaction = async (messageId: number, emoji: string): Promise<void> => {
    try {
      const authStore = useAuthStore()
      const response = await fetch(`http://localhost:3001/api/messages/${messageId}/reactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emoji })
      })

      if (!response.ok) {
        throw new Error('Failed to add reaction')
      }

      const result = await response.json()

      // Update local message reactions
      updateMessageReactions(messageId, result.data.reactions)

      // Emit socket event for real-time updates
      if (authStore.socket) {
        authStore.socket.emit('add_reaction', { message_id: messageId, emoji })
      }
    } catch (err) {
      console.error('Add reaction error:', err)
      error.value = 'Failed to add reaction'
    }
  }

  const updateMessageReactions = (messageId: number, reactions: MessageReaction[]): void => {
    // Update reactions in all conversations
    conversations.value.forEach(conversation => {
      const messageIndex = conversation.messages?.findIndex(msg => msg.id === messageId)
      if (messageIndex !== undefined && messageIndex >= 0 && conversation.messages) {
        conversation.messages[messageIndex].reactions = reactions
      }
    })
  }

  const handleReactionUpdate = (data: {
    message_id: number
    action: 'added' | 'removed'
    reactions: MessageReaction[]
    user_id: number
    emoji: string
  }): void => {
    updateMessageReactions(data.message_id, data.reactions)

    // Show notification for reactions on current user's messages
    const authStore = useAuthStore()
    if (data.action === 'added' && data.user_id !== authStore.user?.id) {
      // Find the message that was reacted to
      for (const [conversationId, conversationMessages] of messages.value) {
        const message = conversationMessages.find(m => m.id === data.message_id)
        if (message && message.sender.id === authStore.user?.id) {
          // Find the user who reacted
          const conversation = conversations.value.find(c => c.id === conversationId)
          const reactor = conversation?.participants.find(p => p.id === data.user_id)

          if (reactor) {
            notificationService.showReactionNotification(
              reactor.display_name,
              data.emoji,
              message.content || 'your message'
            ).catch(error => {
              console.warn('Failed to show reaction notification:', error)
            })
          }
          break
        }
      }
    }
  }

  const setReplyToMessage = (message: Message): void => {
    replyToMessage.value = message
  }

  const clearReplyToMessage = (): void => {
    replyToMessage.value = null
  }

  const sendReply = async (conversationId: number, content: string): Promise<void> => {
    if (!replyToMessage.value) return

    const replyToId = replyToMessage.value.id
    clearReplyToMessage()

    await sendMessage(conversationId, content, replyToId)
  }

  const sendReplyWithAttachments = async (
    conversationId: number,
    content: string,
    attachments: MessageAttachment[]
  ): Promise<void> => {
    if (!replyToMessage.value) return

    const replyToId = replyToMessage.value.id
    clearReplyToMessage()

    await sendMessageWithAttachments(conversationId, content, attachments, replyToId)
  }

  const clearError = (): void => {
    error.value = null
  }

  return {
    // State
    conversations,
    messages,
    friends,

    activeConversation,
    typingUsers,
    isLoading,
    error,
    showNewConversationModal,
    showCallModal,
    activeCall,
    replyToMessage,

    // Computed
    sortedConversations,
    activeConversationMessages,
    getTypingUsersForConversation,

    // Actions
    loadConversations,
    loadMessages,
    sendMessage,
    sendMessageWithAttachments,
    createConversation,
    loadFriends,
    sendFriendRequest,

    setActiveConversation,
    markMessagesAsRead,
    handleNewMessage,
    updateUserStatus,
    setUserTyping,
    startTyping,
    stopTyping,
    handleIncomingCall,
    handleCallAnswered,
    handleCallDeclined,
    handleCallEnded,
    addReaction,
    updateMessageReactions,
    handleReactionUpdate,
    setReplyToMessage,
    clearReplyToMessage,
    sendReply,
    sendReplyWithAttachments,
    clearError
  }
})
