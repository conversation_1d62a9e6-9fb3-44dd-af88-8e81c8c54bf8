<template>
  <div v-if="showNotification" class="incoming-call-notification">
    <div class="notification-content">
      <div class="caller-info">
        <div class="caller-avatar">
          <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
          </svg>
        </div>
        <div class="caller-details">
          <div class="caller-name">{{ callerName }}</div>
          <div class="call-type">
            <svg v-if="isVideo" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
            </svg>
            <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
            </svg>
            {{ isVideo ? 'Video call' : 'Voice call' }}
          </div>
        </div>
      </div>
      
      <div class="call-actions">
        <button @click="declineCall" class="action-btn decline-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.18.18.29.43.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
          </svg>
        </button>
        
        <button @click="answerCall" class="action-btn answer-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { webrtcService } from '../../services/webrtcService'
import type { MediaConnection } from 'peerjs'

const showNotification = ref(false)
const callerName = ref('')
const callerId = ref('')
const isVideo = ref(false)
let currentCall: MediaConnection | null = null

const answerCall = async () => {
  if (currentCall) {
    try {
      await webrtcService.answerCall(currentCall)
      hideNotification()
    } catch (error) {
      console.error('Failed to answer call:', error)
    }
  }
}

const declineCall = () => {
  if (currentCall) {
    currentCall.close()
  }
  hideNotification()
}

const hideNotification = () => {
  showNotification.value = false
  callerName.value = ''
  callerId.value = ''
  isVideo.value = false
  currentCall = null
}

const handleIncomingCall = (call: MediaConnection, incomingCallerId: string, incomingCallerName: string, incomingIsVideo: boolean) => {
  // Don't show notification if already in a call
  if (webrtcService.callState.isInCall) {
    call.close()
    return
  }

  currentCall = call
  callerId.value = incomingCallerId
  callerName.value = incomingCallerName
  isVideo.value = incomingIsVideo
  showNotification.value = true

  // Set up call state for the incoming call
  webrtcService.callState.currentCall = call
  webrtcService.callState.callerId = incomingCallerId
  webrtcService.callState.callerName = incomingCallerName
  webrtcService.callState.isReceiving = true

  // Auto-hide after 30 seconds
  setTimeout(() => {
    if (showNotification.value) {
      declineCall()
    }
  }, 30000)
}

onMounted(() => {
  webrtcService.onIncomingCall(handleIncomingCall)
})

onUnmounted(() => {
  // Cleanup handled by service
})
</script>

<style scoped>
.incoming-call-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 20px;
  z-index: 1001;
  min-width: 300px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.caller-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.caller-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.caller-details {
  flex: 1;
}

.caller-name {
  font-weight: 600;
  font-size: 1rem;
  color: #111827;
  margin-bottom: 4px;
}

.call-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: #6b7280;
}

.call-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.action-btn:hover {
  transform: scale(1.05);
}

.answer-btn {
  background: #22c55e;
}

.answer-btn:hover {
  background: #16a34a;
}

.decline-btn {
  background: #ef4444;
}

.decline-btn:hover {
  background: #dc2626;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .incoming-call-notification {
    background: #1f2937;
    color: white;
  }
  
  .caller-name {
    color: white;
  }
  
  .caller-avatar {
    background: #374151;
    color: #9ca3af;
  }
}
</style>
