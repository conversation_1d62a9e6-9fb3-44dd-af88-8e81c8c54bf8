<template>
  <div v-if="show" class="emoji-picker-overlay" @click="handleOverlayClick">
    <div class="emoji-picker" @click.stop>
      <div class="emoji-picker-header">
        <span class="picker-title">React with an emoji</span>
        <button class="close-button" @click="$emit('close')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="emoji-grid">
        <button
          v-for="emoji in emojis"
          :key="emoji"
          class="emoji-button"
          @click="selectEmoji(emoji)"
          :title="getEmojiName(emoji)"
        >
          {{ emoji }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'

interface Props {
  show: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'select', emoji: string): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// Common emoji reactions
const emojis = [
  '👍', '👎', '❤️', '😂', '😮', '😢', '😡', '🎉',
  '👏', '🔥', '💯', '✨', '💖', '😍', '🤔', '😊',
  '😎', '🙌', '👌', '💪', '🚀', '⭐', '💝', '🎊'
]

const emojiNames: Record<string, string> = {
  '👍': 'Thumbs up',
  '👎': 'Thumbs down', 
  '❤️': 'Heart',
  '😂': 'Laughing',
  '😮': 'Surprised',
  '😢': 'Sad',
  '😡': 'Angry',
  '🎉': 'Party',
  '👏': 'Clapping',
  '🔥': 'Fire',
  '💯': 'Hundred',
  '✨': 'Sparkles',
  '💖': 'Sparkling heart',
  '😍': 'Heart eyes',
  '🤔': 'Thinking',
  '😊': 'Smiling',
  '😎': 'Cool',
  '🙌': 'Raised hands',
  '👌': 'OK hand',
  '💪': 'Flexed biceps',
  '🚀': 'Rocket',
  '⭐': 'Star',
  '💝': 'Gift heart',
  '🎊': 'Confetti'
}

const selectEmoji = (emoji: string) => {
  emit('select', emoji)
  emit('close')
}

const handleOverlayClick = () => {
  emit('close')
}

const getEmojiName = (emoji: string): string => {
  return emojiNames[emoji] || emoji
}
</script>

<style scoped>
.emoji-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.emoji-picker {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 16px;
  max-width: 320px;
  width: 90vw;
  max-height: 400px;
  overflow-y: auto;
}

.emoji-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.picker-title {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
}

.emoji-button {
  background: none;
  border: none;
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
}

.emoji-button:hover {
  background: #f3f4f6;
  transform: scale(1.1);
}

.emoji-button:active {
  transform: scale(0.95);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .emoji-picker {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .emoji-picker-header {
    border-bottom-color: #374151;
  }
  
  .picker-title {
    color: #f9fafb;
  }
  
  .close-button {
    color: #9ca3af;
  }
  
  .close-button:hover {
    background: #374151;
    color: #f9fafb;
  }
  
  .emoji-button:hover {
    background: #374151;
  }
}
</style>
