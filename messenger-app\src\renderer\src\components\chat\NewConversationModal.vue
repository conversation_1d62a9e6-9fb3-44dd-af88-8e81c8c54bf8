<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useChatStore } from '../../stores/chat'
import { useAuthStore } from '../../stores/auth'
import type { Friend } from '../../stores/chat'

const chatStore = useChatStore()
const authStore = useAuthStore()

const emit = defineEmits<{
  close: []
}>()

// Modal state
const conversationType = ref<'direct' | 'group'>('direct')
const selectedFriends = ref<number[]>([])
const groupName = ref('')
const groupDescription = ref('')
const searchQuery = ref('')
const isCreating = ref(false)

// Computed
const filteredFriends = computed(() => {
  if (!searchQuery.value) return chatStore.friends

  const query = searchQuery.value.toLowerCase()
  return chatStore.friends.filter(friend =>
    friend.display_name.toLowerCase().includes(query) ||
    friend.username.toLowerCase().includes(query)
  )
})

const canCreateConversation = computed(() => {
  if (conversationType.value === 'direct') {
    return selectedFriends.value.length === 1
  } else {
    return selectedFriends.value.length >= 1 && groupName.value.trim().length > 0
  }
})

// Methods
const toggleFriendSelection = (friendId: number) => {
  const index = selectedFriends.value.indexOf(friendId)
  if (index > -1) {
    selectedFriends.value.splice(index, 1)
  } else {
    if (conversationType.value === 'direct') {
      selectedFriends.value = [friendId] // Only one friend for direct conversations
    } else {
      selectedFriends.value.push(friendId)
    }
  }
}

const isFriendSelected = (friendId: number): boolean => {
  return selectedFriends.value.includes(friendId)
}

const createConversation = async () => {
  if (!canCreateConversation.value) return

  isCreating.value = true
  
  try {
    const conversation = await chatStore.createConversation(
      conversationType.value,
      selectedFriends.value,
      conversationType.value === 'group' ? groupName.value.trim() : undefined,
      conversationType.value === 'group' ? groupDescription.value.trim() : undefined
    )

    if (conversation) {
      // Set the new conversation as active
      await chatStore.setActiveConversation(conversation)
      // Close the modal
      closeModal()
    }
  } catch (error) {
    console.error('Failed to create conversation:', error)
  } finally {
    isCreating.value = false
  }
}

const closeModal = () => {
  chatStore.showNewConversationModal = false
  emit('close')
}

const resetForm = () => {
  conversationType.value = 'direct'
  selectedFriends.value = []
  groupName.value = ''
  groupDescription.value = ''
  searchQuery.value = ''
}

// Watch for conversation type changes
const onConversationTypeChange = () => {
  selectedFriends.value = []
}



onMounted(() => {
  // Load friends if not already loaded
  if (chatStore.friends.length === 0) {
    chatStore.loadFriends()
  }
})
</script>

<template>
  <div class="modal-overlay" @click.self="closeModal">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header">
        <h2 class="modal-title">New Conversation</h2>
        <button class="close-button" @click="closeModal">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <!-- Conversation Type Selection -->
        <div class="form-group">
          <label class="form-label">Conversation Type</label>
          <div class="type-selector">
            <button
              class="type-button"
              :class="{ active: conversationType === 'direct' }"
              @click="conversationType = 'direct'; onConversationTypeChange()"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
              Direct Message
            </button>
            <button
              class="type-button"
              :class="{ active: conversationType === 'group' }"
              @click="conversationType = 'group'; onConversationTypeChange()"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
              Group Chat
            </button>
          </div>
        </div>

        <!-- Group Details (only for group conversations) -->
        <div v-if="conversationType === 'group'" class="form-group">
          <label class="form-label" for="groupName">Group Name *</label>
          <input
            id="groupName"
            v-model="groupName"
            type="text"
            class="form-input"
            placeholder="Enter group name"
            maxlength="50"
          />
        </div>

        <div v-if="conversationType === 'group'" class="form-group">
          <label class="form-label" for="groupDescription">Description (optional)</label>
          <textarea
            id="groupDescription"
            v-model="groupDescription"
            class="form-textarea"
            placeholder="Enter group description"
            rows="3"
            maxlength="200"
          ></textarea>
        </div>

        <!-- Friends Search -->
        <div class="form-group">
          <label class="form-label">
            {{ conversationType === 'direct' ? 'Select Friend' : 'Select Friends' }}
          </label>
          <div class="search-input-wrapper">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              class="search-input"
              placeholder="Search friends..."
            />
          </div>
        </div>

        <!-- Friends List -->
        <div class="friends-list">
          <div v-if="chatStore.isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>Loading friends...</p>
          </div>

          <div v-else-if="filteredFriends.length === 0" class="empty-state">
            <p>{{ searchQuery ? 'No friends found' : 'No friends yet' }}</p>
          </div>

          <div v-else class="friends-grid">
            <div
              v-for="friend in filteredFriends"
              :key="friend.id"
              class="friend-item"
              :class="{ selected: isFriendSelected(friend.id) }"
              @click="toggleFriendSelection(friend.id)"
            >
              <div class="friend-avatar">
                <img 
                  v-if="friend.avatar_url"
                  :src="friend.avatar_url"
                  :alt="friend.display_name"
                  class="avatar-image"
                />
                <div v-else class="avatar-placeholder">
                  {{ friend.display_name.charAt(0).toUpperCase() }}
                </div>
                <div class="status-indicator" :class="friend.status"></div>
              </div>
              <div class="friend-info">
                <h4 class="friend-name">{{ friend.display_name }}</h4>
                <p class="friend-username">@{{ friend.username }}</p>
              </div>
              <div v-if="isFriendSelected(friend.id)" class="selected-indicator">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="20,6 9,17 4,12"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button class="cancel-button" @click="closeModal" :disabled="isCreating">
          Cancel
        </button>
        <button 
          class="create-button" 
          @click="createConversation"
          :disabled="!canCreateConversation || isCreating"
        >
          <div v-if="isCreating" class="loading-spinner small"></div>
          {{ isCreating ? 'Creating...' : 'Create Conversation' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  flex-shrink: 0;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.type-selector {
  display: flex;
  gap: 12px;
}

.type-button {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.type-button:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.type-button.active {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.friends-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin-right: 8px;
  margin-bottom: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.friends-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.friend-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.friend-item:hover {
  background: #f9fafb;
}

.friend-item.selected {
  background: #f0f4ff;
  border-color: #667eea;
}

.friend-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar-image,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar-image {
  object-fit: cover;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.away {
  background: #f59e0b;
}

.status-indicator.offline {
  background: #6b7280;
}

.friend-info {
  flex: 1;
}

.friend-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 2px 0;
}

.friend-username {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.selected-indicator {
  color: #667eea;
  margin-left: 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.cancel-button,
.create-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.cancel-button {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.cancel-button:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.create-button {
  background: #667eea;
  border: 1px solid #667eea;
  color: white;
}

.create-button:hover:not(:disabled) {
  background: #5a6fd8;
  border-color: #5a6fd8;
}

.create-button:disabled,
.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
