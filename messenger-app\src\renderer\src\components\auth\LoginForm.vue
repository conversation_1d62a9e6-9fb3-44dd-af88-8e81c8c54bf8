<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { MessageCircle, Mail, Eye, EyeOff, Loader2, CheckCircle, AlertCircle, CheckIcon } from 'lucide-vue-next'

const emit = defineEmits<{
  switchToRegister: []
}>()

const authStore = useAuthStore()

// Form state
const form = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// UI state
const showPassword = ref(false)
const focusedField = ref('')
const showToast = ref(false)
const toastMessage = ref('')

// Validation errors
const errors = reactive({
  username: '',
  password: ''
})

// Computed properties
const isLoading = computed(() => authStore.isLoading)

// Focus handlers
const handleFocus = (field: string) => {
  focusedField.value = field
}

const handleBlur = (field: string) => {
  focusedField.value = ''
  if (field === 'username') validateUsername()
  if (field === 'password') validatePassword()
}

// Input handlers with debounced validation
const handleUsernameInput = () => {
  if (errors.username) {
    setTimeout(validateUsername, 300)
  }
}

const handlePasswordInput = () => {
  if (errors.password) {
    setTimeout(validatePassword, 300)
  }
}

// Password toggle with animation
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// Validation functions
const validateUsername = () => {
  if (!form.username) {
    errors.username = 'Username or email is required'
  } else {
    errors.username = ''
  }
}

const validatePassword = () => {
  if (!form.password) {
    errors.password = 'Password is required'
  } else if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
  } else {
    errors.password = ''
  }
}

// Show toast notification
const showToastNotification = (message: string) => {
  toastMessage.value = message
  showToast.value = true
  setTimeout(() => {
    showToast.value = false
  }, 3000)
}

// Form handlers
const handleLogin = async () => {
  // Clear any previous auth errors
  authStore.clearError()

  validateUsername()
  validatePassword()

  if (errors.username || errors.password) {
    // Shake animation for errors
    const formElement = document.querySelector('.login-card')
    formElement?.classList.add('shake')
    setTimeout(() => formElement?.classList.remove('shake'), 500)
    return
  }

  try {
    const success = await authStore.login({
      username: form.username.trim(),
      password: form.password
    })

    if (success) {
      showToastNotification('Login successful!')
    }
    // Error handling is done by the auth store
  } catch (error) {
    console.error('Login failed:', error)
  }
}

const handleCreateAccount = () => {
  emit('switchToRegister')
}
</script>

<template>
  <div class="login-form">
    <!-- Logo Section with Entrance Animation -->
    <transition name="logo-entrance" appear>
      <div class="text-center mb-8">
        <div class="logo-container inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-4 shadow-lg">
          <MessageCircle class="w-8 h-8 text-white logo-icon" />
        </div>
        <h1 class="text-2xl font-semibold text-gray-900 mb-2 title-text">Welcome Back</h1>
        <p class="text-gray-600 subtitle-text">Sign in to your account</p>
      </div>
    </transition>

    <!-- Login Form with Card Animation -->
    <transition name="card-entrance" appear>
      <div class="login-card">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- Username/Email Input with Floating Label -->
          <div class="input-group" :class="{ 'has-error': errors.username, 'is-focused': focusedField === 'username' }">
            <div class="relative">
              <input
                id="username"
                v-model="form.username"
                type="text"
                required
                class="floating-input w-full px-4 pt-6 pb-2 border border-gray-300 rounded-lg transition-all duration-300 bg-gray-50/50 focus:bg-white/90 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': errors.username }"
                placeholder=" "
                @focus="handleFocus('username')"
                @blur="handleBlur('username')"
                @input="handleUsernameInput"
                :disabled="isLoading"
              />
              <label for="username" class="floating-label absolute left-4 top-4 text-gray-500 transition-all duration-300 pointer-events-none">
                Username or email
              </label>
              <div class="input-icon absolute right-3 top-4">
                <transition name="icon-bounce" mode="out-in">
                  <Mail v-if="!form.username" class="w-5 h-5 text-gray-400" />
                  <CheckCircle v-else-if="!errors.username && form.username" class="w-5 h-5 text-green-500" />
                  <AlertCircle v-else class="w-5 h-5 text-red-500" />
                </transition>
              </div>
            </div>
            <transition name="error-slide">
              <p v-if="errors.username" class="error-message text-sm text-red-600 mt-1">{{ errors.username }}</p>
            </transition>
          </div>

          <!-- Password Input with Floating Label -->
          <div class="input-group" :class="{ 'has-error': errors.password, 'is-focused': focusedField === 'password' }">
            <div class="relative">
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="floating-input w-full px-4 pt-6 pb-2 border border-gray-300 rounded-lg transition-all duration-300 bg-gray-50/50 focus:bg-white/90 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 pr-12"
                :class="{ 'border-red-400 focus:border-red-500 focus:ring-red-200': errors.password }"
                placeholder=" "
                @focus="handleFocus('password')"
                @blur="handleBlur('password')"
                @input="handlePasswordInput"
                :disabled="isLoading"
              />
              <label for="password" class="floating-label absolute left-4 top-4 text-gray-500 transition-all duration-300 pointer-events-none">
                Password
              </label>
              <button
                type="button"
                @click="togglePassword"
                class="password-toggle absolute right-3 top-4 text-gray-400 hover:text-gray-600 transition-all duration-200 hover:scale-110"
                :disabled="isLoading"
              >
                <transition name="icon-flip" mode="out-in">
                  <Eye v-if="!showPassword" class="w-5 h-5" />
                  <EyeOff v-else class="w-5 h-5" />
                </transition>
              </button>
            </div>
            <transition name="error-slide">
              <p v-if="errors.password" class="error-message text-sm text-red-600 mt-1">{{ errors.password }}</p>
            </transition>
          </div>

          <!-- Remember Me -->
          <div class="flex items-center justify-between">
            <label class="checkbox-container flex items-center cursor-pointer group">
              <div class="relative">
                <input
                  v-model="form.rememberMe"
                  type="checkbox"
                  class="sr-only"
                />
                <div class="checkbox-custom w-5 h-5 border-2 border-gray-300 rounded transition-all duration-200 group-hover:border-blue-400">
                  <transition name="check-mark">
                    <CheckIcon v-if="form.rememberMe" class="w-3 h-3 text-white absolute top-0.5 left-0.5" />
                  </transition>
                </div>
              </div>
              <span class="ml-3 text-sm text-gray-600 group-hover:text-gray-800 transition-colors duration-200">Keep me signed in</span>
            </label>
          </div>

          <!-- Auth Store Error Display -->
          <div v-if="authStore.error" class="error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            {{ authStore.error }}
          </div>

          <!-- Enhanced Sign In Button -->
          <button
            type="submit"
            :disabled="isLoading"
            class="signin-button w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center relative overflow-hidden group"
            :class="{ 'loading': isLoading }"
          >
            <div class="button-ripple absolute inset-0 bg-white/20 transform scale-0 rounded-full transition-transform duration-500"></div>
            <transition name="button-content" mode="out-in">
              <div v-if="isLoading" class="flex items-center">
                <Loader2 class="w-5 h-5 mr-2 animate-spin" />
                <span>Signing in...</span>
              </div>
              <span v-else class="relative z-10">Sign in</span>
            </transition>
          </button>
        </form>

        <!-- Create Account Link with Hover Animation -->
        <div class="mt-8 text-center">
          <p class="text-sm text-gray-600">
            Don't have an account?
            <button
              type="button"
              class="create-account text-blue-600 hover:text-blue-800 font-medium transition-all duration-200 ml-1 relative"
              @click="handleCreateAccount"
              :disabled="isLoading"
            >
              <span class="relative z-10">Create one</span>
              <div class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600 transform scale-x-0 origin-left transition-transform duration-300 hover:scale-x-100"></div>
            </button>
          </p>
        </div>
      </div>
    </transition>

    <!-- Success/Error Toast -->
    <transition name="toast">
      <div v-if="showToast" class="fixed top-4 right-4 bg-white rounded-lg shadow-lg p-4 border-l-4 border-green-500 z-50">
        <div class="flex items-center">
          <CheckCircle class="w-5 h-5 text-green-500 mr-2" />
          <span class="text-sm font-medium text-gray-900">{{ toastMessage }}</span>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.login-form {
  width: 100%;
}

/* Logo entrance animation */
.logo-entrance-enter-active {
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.logo-entrance-enter-from {
  opacity: 0;
  transform: translateY(-50px) scale(0.5);
}

.logo-container {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.5); }
}

.title-text {
  animation: slideInUp 0.8s ease-out 0.3s both;
}

.subtitle-text {
  animation: slideInUp 0.8s ease-out 0.5s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card entrance animation */
.card-entrance-enter-active {
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.card-entrance-enter-from {
  opacity: 0;
  transform: translateY(50px) scale(0.95);
}

.login-card {
  animation: cardFloat 0.8s ease-out;
}

@keyframes cardFloat {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Shake animation for errors */
.shake {
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes shake {
  10%, 90% { transform: translate3d(-2px, 0, 0); }
  20%, 80% { transform: translate3d(4px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-8px, 0, 0); }
  40%, 60% { transform: translate3d(8px, 0, 0); }
}

/* Floating label animations */
.floating-input:focus + .floating-label,
.floating-input:not(:placeholder-shown) + .floating-label {
  transform: translateY(-12px) scale(0.85);
  color: #3b82f6;
  font-weight: 500;
}

.input-group.is-focused .floating-input {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.input-group.has-error .floating-input {
  animation: inputError 0.3s ease-in-out;
}

@keyframes inputError {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Icon animations */
.icon-bounce-enter-active, .icon-bounce-leave-active {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.icon-bounce-enter-from, .icon-bounce-leave-to {
  opacity: 0;
  transform: scale(0.5) rotate(180deg);
}

.icon-flip-enter-active, .icon-flip-leave-active {
  transition: all 0.3s ease;
}

.icon-flip-enter-from, .icon-flip-leave-to {
  opacity: 0;
  transform: rotateY(90deg);
}

/* Error message animation */
.error-slide-enter-active, .error-slide-leave-active {
  transition: all 0.3s ease;
}

.error-slide-enter-from, .error-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

.error-slide-enter-to, .error-slide-leave-from {
  max-height: 50px;
}

/* Custom checkbox animation */
.checkbox-custom {
  position: relative;
}

.checkbox-container input:checked + .checkbox-custom {
  background: linear-gradient(45deg, #3b82f6, #6366f1);
  border-color: #3b82f6;
  transform: scale(1.1);
}

.check-mark-enter-active {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.check-mark-enter-from {
  opacity: 0;
  transform: scale(0) rotate(45deg);
}

/* Button animations */
.signin-button {
  position: relative;
  overflow: hidden;
}

.signin-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.signin-button:active:not(:disabled) {
  transform: translateY(0);
}

.signin-button:hover:not(:disabled) .button-ripple {
  transform: scale(1);
}

.button-content-enter-active, .button-content-leave-active {
  transition: all 0.3s ease;
}

.button-content-enter-from, .button-content-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Toast animation */
.toast-enter-active, .toast-leave-active {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.toast-enter-from, .toast-leave-to {
  opacity: 0;
  transform: translateX(100px) scale(0.8);
}

/* Hover effects for links */
.create-account:hover .absolute {
  transform: scaleX(1);
}

/* Loading state */
.signin-button.loading {
  pointer-events: none;
}

/* Responsive animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus visible for accessibility */
.signin-button:focus-visible,
.floating-input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Utility classes for Tailwind-like styling */
.text-center { text-align: center; }
.mb-8 { margin-bottom: 2rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-1 { margin-left: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.inline-flex { display: inline-flex; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.w-3 { width: 0.75rem; }
.h-3 { height: 0.75rem; }
.w-full { width: 100%; }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }
.to-indigo-600 { --tw-gradient-to: #4f46e5; }
.rounded-full { border-radius: 9999px; }
.rounded-lg { border-radius: 0.5rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.text-white { color: #ffffff; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.text-gray-900 { color: #111827; }
.text-gray-600 { color: #4b5563; }
.text-gray-500 { color: #6b7280; }
.text-gray-400 { color: #9ca3af; }
.text-gray-800 { color: #1f2937; }
.text-gray-700 { color: #374151; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-green-500 { color: #10b981; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }
.bg-gray-50\/50 { background-color: rgba(249, 250, 251, 0.5); }
.bg-white\/90 { background-color: rgba(255, 255, 255, 0.9); }
.bg-white { background-color: #ffffff; }
.bg-red-50 { background-color: #fef2f2; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-200 { border-color: #e5e7eb; }
.border-red-200 { border-color: #fecaca; }
.border-red-400 { border-color: #f87171; }
.border-l-4 { border-left-width: 4px; }
.border-green-500 { border-left-color: #10b981; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.pt-6 { padding-top: 1.5rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pr-12 { padding-right: 3rem; }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { margin-top: 1.5rem; }
.absolute { position: absolute; }
.relative { position: relative; }
.fixed { position: fixed; }
.top-4 { top: 1rem; }
.right-4 { right: 1rem; }
.right-3 { right: 0.75rem; }
.left-4 { left: 1rem; }
.top-0\.5 { top: 0.125rem; }
.left-0\.5 { left: 0.125rem; }
.bottom-0 { bottom: 0px; }
.left-0 { left: 0px; }
.h-0\.5 { height: 0.125rem; }
.z-50 { z-index: 50; }
.z-10 { z-index: 10; }
.cursor-pointer { cursor: pointer; }
.pointer-events-none { pointer-events: none; }
.sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }
.duration-200 { transition-duration: 200ms; }
.focus\:bg-white\/90:focus { background-color: rgba(255, 255, 255, 0.9); }
.focus\:border-blue-500:focus { border-color: #3b82f6; }
.focus\:border-red-500:focus { border-color: #ef4444; }
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-blue-200:focus { --tw-ring-color: #dbeafe; }
.focus\:ring-red-200:focus { --tw-ring-color: #fecaca; }
.hover\:text-gray-600:hover { color: #4b5563; }
.hover\:text-gray-800:hover { color: #1f2937; }
.hover\:text-blue-800:hover { color: #1e40af; }
.hover\:border-blue-400:hover { border-color: #60a5fa; }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-x-100:hover { transform: scaleX(1); }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.group:hover .group-hover\:text-gray-800 { color: #1f2937; }
.group:hover .group-hover\:border-blue-400 { border-color: #60a5fa; }
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; }
.scale-x-0 { --tw-scale-x: 0; }
.origin-left { transform-origin: left; }
.overflow-hidden { overflow: hidden; }
.animate-spin { animation: spin 1s linear infinite; }
@keyframes spin { to { transform: rotate(360deg); } }

</style>
