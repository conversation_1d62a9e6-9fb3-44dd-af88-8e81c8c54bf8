<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>Add Friend</h2>
        <button class="close-button" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <div class="form-group">
          <label for="username">Username</label>
          <input
            id="username"
            v-model="username"
            type="text"
            placeholder="Enter username to add as friend"
            class="username-input"
            @keyup.enter="sendFriendRequest"
          />
        </div>
        
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
        
        <div v-if="success" class="success-message">
          {{ success }}
        </div>
      </div>
      
      <div class="modal-actions">
        <button 
          class="cancel-button" 
          @click="closeModal"
        >
          Cancel
        </button>
        <button 
          class="add-button" 
          @click="sendFriendRequest"
          :disabled="!username.trim() || isLoading"
        >
          <span v-if="isLoading">Adding...</span>
          <span v-else>Add Friend</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useChatStore } from '../../stores/chat'

const chatStore = useChatStore()

// Props
interface Props {
  isVisible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// State
const username = ref('')
const isLoading = ref(false)
const error = ref<string | null>(null)
const success = ref<string | null>(null)

// Methods
const closeModal = () => {
  resetForm()
  emit('close')
}

const resetForm = () => {
  username.value = ''
  error.value = null
  success.value = null
  isLoading.value = false
}

const sendFriendRequest = async () => {
  if (!username.value.trim()) {
    error.value = 'Please enter a username'
    return
  }

  isLoading.value = true
  error.value = null
  success.value = null

  try {
    await chatStore.sendFriendRequest(username.value.trim())
    success.value = `Contact added successfully!`
    username.value = ''

    // Refresh the friends list to show the newly added friend
    await chatStore.loadFriends()

    // Auto close after 2 seconds
    setTimeout(() => {
      closeModal()
    }, 2000)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to add contact'
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--color-surface-elevated);
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 400px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-border-primary);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--color-border-primary);
}

.modal-header h2 {
  margin: 0;
  color: var(--color-text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.close-button:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.username-input {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  background: var(--color-surface-secondary);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: border-color var(--transition-fast);
}

.username-input:focus {
  outline: none;
  border-color: var(--color-border-accent);
}

.username-input::placeholder {
  color: var(--color-text-tertiary);
}

.error-message {
  color: var(--color-text-error);
  font-size: 14px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(220, 38, 38, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-error);
}

.success-message {
  color: var(--color-text-success);
  font-size: 14px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(5, 150, 105, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-success);
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--color-border-primary);
  justify-content: flex-end;
}

.cancel-button {
  padding: 10px 20px;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.cancel-button:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.add-button {
  padding: 10px 20px;
  border: none;
  border-radius: var(--radius-lg);
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.add-button:hover:not(:disabled) {
  background: var(--color-primary-700);
}

.add-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
