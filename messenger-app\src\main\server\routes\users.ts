import { Router, Response } from 'express';
import { executeQuery } from '../../database/config';
import { UserRow, FriendshipRow } from '../../database/models';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

/**
 * Get all users (for user search/discovery)
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const currentUserId = req.user!.userId;
    const { search, limit = 20, offset = 0 } = req.query;

    // Debug logging
    console.log('=== GET /api/users ===');
    console.log('Raw query params:', req.query);
    console.log('Search param:', JSON.stringify(search), 'Type:', typeof search);
    console.log('Limit param:', limit, 'Offset param:', offset);

    // Ensure limit and offset are valid numbers
    const limitNum = Math.max(1, Math.min(100, parseInt(limit as string) || 20));
    const offsetNum = Math.max(0, parseInt(offset as string) || 0);

    // Determine if we should add search condition
    const hasSearch = search && typeof search === 'string' && search.trim().length > 0;
    console.log('Has search:', hasSearch, 'Search value:', hasSearch ? search.trim() : 'N/A');

    let users: UserRow[];

    if (hasSearch) {
      // Query with search
      const searchTerm = `%${search.trim()}%`;
      console.log('Executing search query with term:', searchTerm);

      users = await executeQuery<UserRow[]>(`
        SELECT id, username, display_name, avatar_url, status, last_seen
        FROM users
        WHERE id != ? AND (username LIKE ? OR display_name LIKE ?)
        ORDER BY display_name
        LIMIT ? OFFSET ?
      `, [currentUserId, searchTerm, searchTerm, limitNum, offsetNum]);
    } else {
      // Query without search
      console.log('Executing query without search');

      users = await executeQuery<UserRow[]>(`
        SELECT id, username, display_name, avatar_url, status, last_seen
        FROM users
        WHERE id != ?
        ORDER BY display_name
        LIMIT ? OFFSET ?
      `, [currentUserId, limitNum, offsetNum]);
    }

    console.log('Query executed successfully, returned', users.length, 'users');

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get users',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Get user's friends
 */
router.get('/friends', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const friends = await executeQuery<any[]>(`
      SELECT
        u.id, u.username, u.display_name, u.avatar_url, u.status, u.last_seen,
        f.created_at as friendship_created_at
      FROM friendships f
      JOIN users u ON (
        CASE
          WHEN f.requester_id = ? THEN u.id = f.addressee_id
          ELSE u.id = f.requester_id
        END
      )
      WHERE (f.requester_id = ? OR f.addressee_id = ?)
        AND f.status = 'accepted'
      ORDER BY u.display_name
    `, [userId, userId, userId]);

    res.json({
      success: true,
      data: friends
    });
  } catch (error) {
    console.error('Get friends error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get friends',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Get pending friend requests
 */
router.get('/friends/requests', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const requests = await executeQuery<any[]>(`
      SELECT
        f.id as friendship_id,
        f.status,
        f.created_at,
        u.id as user_id,
        u.username,
        u.display_name,
        u.avatar_url,
        CASE
          WHEN f.requester_id = ? THEN 'sent'
          ELSE 'received'
        END as request_type
      FROM friendships f
      JOIN users u ON (
        CASE
          WHEN f.requester_id = ? THEN u.id = f.addressee_id
          ELSE u.id = f.requester_id
        END
      )
      WHERE (f.requester_id = ? OR f.addressee_id = ?)
        AND f.status = 'pending'
      ORDER BY f.created_at DESC
    `, [userId, userId, userId, userId]);

    res.json({
      success: true,
      data: requests
    });
  } catch (error) {
    console.error('Get friend requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get friend requests',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Get user by ID
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    console.log('GET /api/users/:id - Raw ID param:', req.params.id, 'Type:', typeof req.params.id);
    const userId = parseInt(req.params.id);
    console.log('Parsed userId:', userId, 'isNaN:', isNaN(userId));

    if (isNaN(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID parameter'
      });
    }

    const users = await executeQuery<UserRow[]>(
      'SELECT id, username, display_name, avatar_url, status, last_seen, created_at FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: users[0]
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Update user profile
 */
router.put('/profile', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { display_name, avatar_url, status } = req.body;

    // Build dynamic update query
    const updates: string[] = [];
    const params: any[] = [];

    if (display_name) {
      updates.push('display_name = ?');
      params.push(display_name);
    }

    if (avatar_url !== undefined) {
      updates.push('avatar_url = ?');
      params.push(avatar_url);
    }

    if (status && ['online', 'offline', 'away', 'busy'].includes(status)) {
      updates.push('status = ?');
      params.push(status);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(userId);

    await executeQuery(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      params
    );

    // Get updated user
    const updatedUser = await executeQuery<UserRow[]>(
      'SELECT id, username, display_name, avatar_url, status, last_seen, created_at, updated_at FROM users WHERE id = ?',
      [userId]
    );

    res.json({
      success: true,
      data: updatedUser[0],
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Send friend request
 */
router.post('/friends/request', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const requesterId = req.user!.userId;
    const { addressee_id } = req.body;

    if (!addressee_id) {
      return res.status(400).json({
        success: false,
        message: 'Addressee ID is required'
      });
    }

    if (requesterId === addressee_id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send friend request to yourself'
      });
    }

    // Check if addressee exists
    const addressee = await executeQuery<UserRow[]>(
      'SELECT id FROM users WHERE id = ?',
      [addressee_id]
    );

    if (addressee.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if friendship already exists
    const existingFriendship = await executeQuery<FriendshipRow[]>(
      'SELECT * FROM friendships WHERE (requester_id = ? AND addressee_id = ?) OR (requester_id = ? AND addressee_id = ?)',
      [requesterId, addressee_id, addressee_id, requesterId]
    );

    if (existingFriendship.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Friendship request already exists or users are already friends'
      });
    }

    // Create friend request
    await executeQuery(
      'INSERT INTO friendships (requester_id, addressee_id, status) VALUES (?, ?, ?)',
      [requesterId, addressee_id, 'pending']
    );

    res.status(201).json({
      success: true,
      message: 'Friend request sent successfully'
    });
  } catch (error) {
    console.error('Send friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send friend request',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Respond to friend request
 */
router.put('/friends/respond', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { friendship_id, action } = req.body;

    if (!friendship_id || !action || !['accept', 'decline'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Friendship ID and valid action (accept/decline) are required'
      });
    }

    // Check if friendship request exists and user is the addressee
    const friendship = await executeQuery<FriendshipRow[]>(
      'SELECT * FROM friendships WHERE id = ? AND addressee_id = ? AND status = ?',
      [friendship_id, userId, 'pending']
    );

    if (friendship.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found or already processed'
      });
    }

    const newStatus = action === 'accept' ? 'accepted' : 'declined';

    await executeQuery(
      'UPDATE friendships SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newStatus, friendship_id]
    );

    res.json({
      success: true,
      message: `Friend request ${action}ed successfully`
    });
  } catch (error) {
    console.error('Respond to friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to respond to friend request',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

export function setupUserRoutes(app: any): void {
  app.use('/api/users', router);
}
