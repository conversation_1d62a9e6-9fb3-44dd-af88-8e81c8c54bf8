<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'

const emit = defineEmits<{
  switchToLogin: []
}>()

const authStore = useAuthStore()

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  display_name: ''
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isSubmitting = ref(false)

// Form validation
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return !form.email || emailRegex.test(form.email)
})

const isValidUsername = computed(() => {
  return form.username.length >= 3 && form.username.length <= 20
})

const isValidPassword = computed(() => {
  return form.password.length >= 6
})

const passwordsMatch = computed(() => {
  return form.password === form.confirmPassword
})

const isFormValid = computed(() => {
  return (
    form.username.trim() &&
    form.email.trim() &&
    form.password.trim() &&
    form.confirmPassword.trim() &&
    form.display_name.trim() &&
    isValidEmail.value &&
    isValidUsername.value &&
    isValidPassword.value &&
    passwordsMatch.value
  )
})

const handleSubmit = async () => {
  if (isSubmitting.value || !isFormValid.value) return

  // Clear any previous errors
  authStore.clearError()

  isSubmitting.value = true

  try {
    const success = await authStore.register({
      username: form.username.trim(),
      email: form.email.trim(),
      password: form.password,
      display_name: form.display_name.trim()
    })

    if (!success) {
      // Error is already set in the store
      console.error('Registration failed')
    }
  } catch (error) {
    console.error('Registration error:', error)
  } finally {
    isSubmitting.value = false
  }
}

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPasswordVisibility = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}
</script>

<template>
  <div class="register-form">
    <h2 class="form-title">Create Account</h2>
    <p class="form-subtitle">Join our community today</p>

    <form @submit.prevent="handleSubmit" class="form">
      <div class="form-group">
        <label for="display_name" class="form-label">Display Name</label>
        <input
          id="display_name"
          v-model="form.display_name"
          type="text"
          class="form-input"
          placeholder="Enter your display name"
          required
          :disabled="isSubmitting"
        />
      </div>

      <div class="form-group">
        <label for="username" class="form-label">Username</label>
        <input
          id="username"
          v-model="form.username"
          type="text"
          class="form-input"
          :class="{ 'error': form.username && !isValidUsername }"
          placeholder="Enter your username"
          required
          :disabled="isSubmitting"
        />
        <div v-if="form.username && !isValidUsername" class="field-error">
          Username must be 3-20 characters long
        </div>
      </div>

      <div class="form-group">
        <label for="email" class="form-label">Email</label>
        <input
          id="email"
          v-model="form.email"
          type="email"
          class="form-input"
          :class="{ 'error': form.email && !isValidEmail }"
          placeholder="Enter your email"
          required
          :disabled="isSubmitting"
        />
        <div v-if="form.email && !isValidEmail" class="field-error">
          Please enter a valid email address
        </div>
      </div>

      <div class="form-group">
        <label for="password" class="form-label">Password</label>
        <div class="password-input-container">
          <input
            id="password"
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            class="form-input"
            :class="{ 'error': form.password && !isValidPassword }"
            placeholder="Enter your password"
            required
            :disabled="isSubmitting"
          />
          <button
            type="button"
            class="password-toggle"
            @click="togglePasswordVisibility"
            :disabled="isSubmitting"
          >
            <svg v-if="showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
              <line x1="1" y1="1" x2="23" y2="23"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
          </button>
        </div>
        <div v-if="form.password && !isValidPassword" class="field-error">
          Password must be at least 6 characters long
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword" class="form-label">Confirm Password</label>
        <div class="password-input-container">
          <input
            id="confirmPassword"
            v-model="form.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            class="form-input"
            :class="{ 'error': form.confirmPassword && !passwordsMatch }"
            placeholder="Confirm your password"
            required
            :disabled="isSubmitting"
          />
          <button
            type="button"
            class="password-toggle"
            @click="toggleConfirmPasswordVisibility"
            :disabled="isSubmitting"
          >
            <svg v-if="showConfirmPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
              <line x1="1" y1="1" x2="23" y2="23"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
          </button>
        </div>
        <div v-if="form.confirmPassword && !passwordsMatch" class="field-error">
          Passwords do not match
        </div>
      </div>

      <div v-if="authStore.error" class="error-message">
        {{ authStore.error }}
      </div>

      <button
        type="submit"
        class="submit-button"
        :disabled="isSubmitting || !isFormValid"
      >
        <span v-if="isSubmitting" class="loading-spinner"></span>
        {{ isSubmitting ? 'Creating Account...' : 'Create Account' }}
      </button>
    </form>

    <div class="form-footer">
      <p class="switch-form-text">
        Already have an account?
        <button
          type="button"
          class="switch-form-button"
          @click="emit('switchToLogin')"
          :disabled="isSubmitting"
        >
          Sign in
        </button>
      </p>
    </div>
  </div>
</template>

<style scoped>
.register-form {
  width: 100%;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  text-align: center;
}

.form-subtitle {
  color: #666;
  margin: 0 0 24px 0;
  text-align: center;
  font-size: 0.9rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #e53e3e;
}

.form-input.error:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.password-toggle:hover:not(:disabled) {
  color: #333;
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.field-error {
  color: #e53e3e;
  font-size: 0.8rem;
  margin-top: 4px;
}

.error-message {
  background: #fee;
  color: #c53030;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  border: 1px solid #fed7d7;
}

.submit-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 8px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.form-footer {
  margin-top: 24px;
  text-align: center;
}

.switch-form-text {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.switch-form-button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: 0.9rem;
}

.switch-form-button:hover:not(:disabled) {
  color: #764ba2;
}

.switch-form-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
