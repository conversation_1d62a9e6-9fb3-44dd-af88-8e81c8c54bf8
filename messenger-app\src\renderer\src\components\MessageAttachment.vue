<template>
  <div class="message-attachment" :class="{ 'is-image': attachment.isImage }">
    <!-- Image Attachment -->
    <div v-if="attachment.isImage" class="image-attachment">
      <img 
        :src="attachment.url" 
        :alt="attachment.originalName"
        @click="openImageModal"
        class="attachment-image"
        loading="lazy"
      >
      <div class="image-overlay">
        <button @click="downloadFile" class="download-btn" title="Download">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- File Attachment -->
    <div v-else class="file-attachment" @click="downloadFile">
      <div class="file-icon">
        <svg v-if="isDocument" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        <svg v-else-if="isAudio" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
        <svg v-else-if="isVideo" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="23 7 16 12 23 17 23 7"/>
          <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
        </svg>
        <svg v-else width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
      </div>
      <div class="file-info">
        <p class="file-name">{{ attachment.originalName }}</p>
        <p class="file-size">{{ formatFileSize(attachment.size) }}</p>
        <p class="file-type">{{ getFileType(attachment.mimetype) }}</p>
      </div>
      <div class="download-icon">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
          <polyline points="7,10 12,15 17,10"/>
          <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <button @click="closeImageModal" class="modal-close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
        <img :src="attachment.url" :alt="attachment.originalName" class="modal-image">
        <div class="modal-info">
          <h3>{{ attachment.originalName }}</h3>
          <p>{{ formatFileSize(attachment.size) }}</p>
          <button @click="downloadFile" class="modal-download">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            Download
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface Attachment {
  id: string;
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  isImage: boolean;
  uploadedAt: string;
}

const props = defineProps<{
  attachment: Attachment;
}>();

const showImageModal = ref(false);

const isDocument = computed(() => {
  return props.attachment.mimetype.includes('pdf') || 
         props.attachment.mimetype.includes('document') ||
         props.attachment.mimetype.includes('text') ||
         props.attachment.mimetype.includes('msword');
});

const isAudio = computed(() => {
  return props.attachment.mimetype.startsWith('audio/');
});

const isVideo = computed(() => {
  return props.attachment.mimetype.startsWith('video/');
});

const openImageModal = () => {
  if (props.attachment.isImage) {
    showImageModal.value = true;
  }
};

const closeImageModal = () => {
  showImageModal.value = false;
};

const downloadFile = async () => {
  try {
    const response = await fetch(props.attachment.url);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = props.attachment.originalName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download failed:', error);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileType = (mimetype: string): string => {
  if (mimetype.includes('pdf')) return 'PDF Document';
  if (mimetype.includes('msword') || mimetype.includes('wordprocessingml')) return 'Word Document';
  if (mimetype.includes('text')) return 'Text File';
  if (mimetype.includes('zip')) return 'Archive';
  if (mimetype.startsWith('audio/')) return 'Audio File';
  if (mimetype.startsWith('video/')) return 'Video File';
  if (mimetype.startsWith('image/')) return 'Image File';
  return 'File';
};
</script>

<style scoped>
.message-attachment {
  margin: 8px 0;
  max-width: 300px;
}

.image-attachment {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-attachment:hover {
  transform: scale(1.02);
}

.attachment-image {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-attachment:hover .image-overlay {
  opacity: 1;
}

.download-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.download-btn:hover {
  background: rgba(0, 0, 0, 0.9);
}

.file-attachment {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f3f4f6;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: 1px solid #e5e7eb;
}

.file-attachment:hover {
  background: #e5e7eb;
}

.file-icon {
  margin-right: 12px;
  color: #6b7280;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 2px 0;
}

.file-type {
  font-size: 11px;
  color: #9ca3af;
  margin: 0;
  text-transform: uppercase;
  font-weight: 500;
}

.download-icon {
  color: #6b7280;
  flex-shrink: 0;
}

/* Image Modal */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}

.modal-info {
  margin-top: 16px;
  text-align: center;
  color: white;
}

.modal-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-info p {
  margin: 0 0 16px 0;
  font-size: 14px;
  opacity: 0.8;
}

.modal-download {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.modal-download:hover {
  background: #4338ca;
}

/* Responsive */
@media (max-width: 768px) {
  .message-attachment {
    max-width: 250px;
  }
  
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }
  
  .modal-image {
    max-height: 60vh;
  }
}
</style>
