/* Theme System - CSS Custom Properties */

/* Base theme variables - Light Mode */
:root,
.theme-light {
  /* Primary Brand Colors */
  --color-primary-50: #eef2ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #6366f1;
  --color-primary-600: #4f46e5;
  --color-primary-700: #4338ca;
  --color-primary-800: #3730a3;
  --color-primary-900: #312e81;

  /* Surface Colors */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f9fafb;
  --color-surface-tertiary: #f3f4f6;
  --color-surface-elevated: #ffffff;
  --color-surface-overlay: rgba(0, 0, 0, 0.5);
  --color-surface-hover: #f3f4f6;
  --color-surface-active: #e5e7eb;

  /* Text Colors */
  --color-text-primary: #111827;
  --color-text-secondary: #374151;
  --color-text-tertiary: #6b7280;
  --color-text-muted: #9ca3af;
  --color-text-inverse: #ffffff;
  --color-text-accent: #6366f1;
  --color-text-success: #059669;
  --color-text-warning: #d97706;
  --color-text-error: #dc2626;

  /* Border Colors */
  --color-border-primary: #e5e7eb;
  --color-border-secondary: #d1d5db;
  --color-border-tertiary: #f3f4f6;
  --color-border-accent: #6366f1;
  --color-border-success: #10b981;
  --color-border-warning: #f59e0b;
  --color-border-error: #ef4444;

  /* Component-Specific Colors */
  
  /* Chat Colors */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #6366f1;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: var(--color-text-inverse);
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-primary);
  --chat-input-border: var(--color-border-primary);
  --chat-typing-background: var(--color-surface-secondary);

  /* Sidebar Colors */
  --sidebar-background: var(--color-surface-secondary);
  --sidebar-item-hover: var(--color-surface-hover);
  --sidebar-item-active: var(--color-primary-100);
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-secondary);
  --sidebar-text-active: var(--color-primary-700);

  /* Button Colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-hover: var(--color-primary-700);
  --button-primary-text: var(--color-text-inverse);
  --button-secondary-bg: var(--color-surface-tertiary);
  --button-secondary-hover: var(--color-surface-active);
  --button-secondary-text: var(--color-text-secondary);

  /* Input Colors */
  --input-background: var(--color-surface-primary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-border-accent);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-muted);

  /* Notification Colors */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 200ms ease;
  --transition-slow: 300ms ease;
}

/* Dark Mode Theme */
.theme-dark {
  /* Surface Colors - Dark */
  --color-surface-primary: #111827;
  --color-surface-secondary: #1f2937;
  --color-surface-tertiary: #374151;
  --color-surface-elevated: #1f2937;
  --color-surface-overlay: rgba(0, 0, 0, 0.7);
  --color-surface-hover: #374151;
  --color-surface-active: #4b5563;

  /* Text Colors - Dark */
  --color-text-primary: #f9fafb;
  --color-text-secondary: #e5e7eb;
  --color-text-tertiary: #d1d5db;
  --color-text-muted: #9ca3af;
  --color-text-inverse: #111827;
  --color-text-accent: #818cf8;
  --color-text-success: #34d399;
  --color-text-warning: #fbbf24;
  --color-text-error: #f87171;

  /* Border Colors - Dark */
  --color-border-primary: #374151;
  --color-border-secondary: #4b5563;
  --color-border-tertiary: #6b7280;
  --color-border-accent: #818cf8;
  --color-border-success: #34d399;
  --color-border-warning: #fbbf24;
  --color-border-error: #f87171;

  /* Component-Specific Colors - Dark */
  
  /* Chat Colors - Dark */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #6366f1;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: var(--color-text-inverse);
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-secondary);
  --chat-input-border: var(--color-border-primary);
  --chat-typing-background: var(--color-surface-tertiary);

  /* Sidebar Colors - Dark */
  --sidebar-background: var(--color-surface-secondary);
  --sidebar-item-hover: var(--color-surface-hover);
  --sidebar-item-active: #312e81;
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-secondary);
  --sidebar-text-active: var(--color-text-accent);

  /* Button Colors - Dark */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-hover: var(--color-primary-500);
  --button-primary-text: var(--color-text-inverse);
  --button-secondary-bg: var(--color-surface-tertiary);
  --button-secondary-hover: var(--color-surface-active);
  --button-secondary-text: var(--color-text-secondary);

  /* Input Colors - Dark */
  --input-background: var(--color-surface-secondary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-border-accent);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-muted);

  /* Notification Colors - Dark */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects - Dark (adjusted for dark theme) */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Ocean Blue Theme */
.theme-ocean {
  /* Base Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Surface Colors */
  --color-surface-primary: #0f172a;
  --color-surface-secondary: #1e293b;
  --color-surface-tertiary: #334155;
  --color-surface-hover: #475569;
  --color-surface-elevated: #1e293b;

  /* Text Colors */
  --color-text-primary: #f1f5f9;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-muted: #64748b;
  --color-text-inverse: #ffffff;
  --color-text-accent: #60a5fa;

  /* Border Colors */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;

  /* Chat Colors */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #2563eb;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: #ffffff;
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-primary);
  --chat-input-border: var(--color-border-primary);

  /* Sidebar Colors */
  --sidebar-background: var(--color-surface-primary);
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-primary);
  --sidebar-item-hover: var(--color-surface-secondary);
  --sidebar-item-active: rgba(37, 99, 235, 0.2);

  /* Button Colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-text: #ffffff;
  --button-primary-hover: var(--color-primary-700);

  /* Input Colors */
  --input-background: var(--color-surface-secondary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-primary-500);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-tertiary);

  /* Notification Colors */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Forest Green Theme */
.theme-forest {
  /* Base Colors */
  --color-primary-50: #f0fdf4;
  --color-primary-100: #dcfce7;
  --color-primary-200: #bbf7d0;
  --color-primary-300: #86efac;
  --color-primary-400: #4ade80;
  --color-primary-500: #22c55e;
  --color-primary-600: #16a34a;
  --color-primary-700: #15803d;
  --color-primary-800: #166534;
  --color-primary-900: #14532d;

  /* Surface Colors */
  --color-surface-primary: #0f1419;
  --color-surface-secondary: #1a2332;
  --color-surface-tertiary: #2d3748;
  --color-surface-hover: #4a5568;
  --color-surface-elevated: #1a2332;

  /* Text Colors */
  --color-text-primary: #f7fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-tertiary: #a0aec0;
  --color-text-muted: #718096;
  --color-text-inverse: #ffffff;
  --color-text-accent: #4ade80;

  /* Border Colors */
  --color-border-primary: #2d3748;
  --color-border-secondary: #4a5568;

  /* Chat Colors */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #16a34a;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: #ffffff;
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-primary);
  --chat-input-border: var(--color-border-primary);

  /* Sidebar Colors */
  --sidebar-background: var(--color-surface-primary);
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-primary);
  --sidebar-item-hover: var(--color-surface-secondary);
  --sidebar-item-active: rgba(22, 163, 74, 0.2);

  /* Button Colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-text: #ffffff;
  --button-primary-hover: var(--color-primary-700);

  /* Input Colors */
  --input-background: var(--color-surface-secondary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-primary-500);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-tertiary);

  /* Notification Colors */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Sunset Orange Theme */
.theme-sunset {
  /* Base Colors */
  --color-primary-50: #fff7ed;
  --color-primary-100: #ffedd5;
  --color-primary-200: #fed7aa;
  --color-primary-300: #fdba74;
  --color-primary-400: #fb923c;
  --color-primary-500: #f97316;
  --color-primary-600: #ea580c;
  --color-primary-700: #c2410c;
  --color-primary-800: #9a3412;
  --color-primary-900: #7c2d12;

  /* Surface Colors */
  --color-surface-primary: #1c1917;
  --color-surface-secondary: #292524;
  --color-surface-tertiary: #44403c;
  --color-surface-hover: #57534e;
  --color-surface-elevated: #292524;

  /* Text Colors */
  --color-text-primary: #fafaf9;
  --color-text-secondary: #e7e5e4;
  --color-text-tertiary: #a8a29e;
  --color-text-muted: #78716c;
  --color-text-inverse: #ffffff;
  --color-text-accent: #fb923c;

  /* Border Colors */
  --color-border-primary: #44403c;
  --color-border-secondary: #57534e;

  /* Chat Colors */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #ea580c;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: #ffffff;
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-primary);
  --chat-input-border: var(--color-border-primary);

  /* Sidebar Colors */
  --sidebar-background: var(--color-surface-primary);
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-primary);
  --sidebar-item-hover: var(--color-surface-secondary);
  --sidebar-item-active: rgba(234, 88, 12, 0.2);

  /* Button Colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-text: #ffffff;
  --button-primary-hover: var(--color-primary-700);

  /* Input Colors */
  --input-background: var(--color-surface-secondary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-primary-500);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-tertiary);

  /* Notification Colors */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Purple Pro Theme */
.theme-purple {
  /* Base Colors */
  --color-primary-50: #faf5ff;
  --color-primary-100: #f3e8ff;
  --color-primary-200: #e9d5ff;
  --color-primary-300: #d8b4fe;
  --color-primary-400: #c084fc;
  --color-primary-500: #a855f7;
  --color-primary-600: #9333ea;
  --color-primary-700: #7c3aed;
  --color-primary-800: #6b21a8;
  --color-primary-900: #581c87;

  /* Surface Colors */
  --color-surface-primary: #1e1b4b;
  --color-surface-secondary: #312e81;
  --color-surface-tertiary: #4338ca;
  --color-surface-hover: #4f46e5;
  --color-surface-elevated: #312e81;

  /* Text Colors */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-tertiary: #cbd5e1;
  --color-text-muted: #94a3b8;
  --color-text-inverse: #ffffff;
  --color-text-accent: #c084fc;

  /* Border Colors */
  --color-border-primary: #4338ca;
  --color-border-secondary: #4f46e5;

  /* Chat Colors */
  --chat-background: var(--color-surface-primary);
  --chat-message-own: #9333ea;
  --chat-message-other: var(--color-surface-tertiary);
  --chat-message-text-own: #ffffff;
  --chat-message-text-other: var(--color-text-primary);
  --chat-input-background: var(--color-surface-primary);
  --chat-input-border: var(--color-border-primary);

  /* Sidebar Colors */
  --sidebar-background: var(--color-surface-primary);
  --sidebar-border: var(--color-border-primary);
  --sidebar-text: var(--color-text-primary);
  --sidebar-item-hover: var(--color-surface-secondary);
  --sidebar-item-active: rgba(147, 51, 234, 0.2);

  /* Button Colors */
  --button-primary-bg: var(--color-primary-600);
  --button-primary-text: #ffffff;
  --button-primary-hover: var(--color-primary-700);

  /* Input Colors */
  --input-background: var(--color-surface-secondary);
  --input-border: var(--color-border-primary);
  --input-border-focus: var(--color-primary-500);
  --input-text: var(--color-text-primary);
  --input-placeholder: var(--color-text-tertiary);

  /* Notification Colors */
  --notification-background: var(--color-surface-elevated);
  --notification-border: var(--color-border-primary);
  --notification-text: var(--color-text-primary);

  /* Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Theme transition for smooth switching */
* {
  transition: background-color var(--transition-fast), 
              border-color var(--transition-fast), 
              color var(--transition-fast);
}

/* Disable transitions during theme initialization */
.theme-transitioning * {
  transition: none !important;
}
