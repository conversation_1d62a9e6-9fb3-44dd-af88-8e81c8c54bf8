import { Router, Response } from 'express';
import { executeQuery } from '../../database/config';
import {
  ConversationRow,
  ConversationParticipantRow,
  CreateConversationData
} from '../../database/models';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

/**
 * Safely parse JSON data that might already be an object
 */
function safeJsonParse(data: any): any {
  if (data === null || data === undefined) {
    return null;
  }

  // If it's already an object or array, return as-is
  if (typeof data === 'object') {
    return data;
  }

  // If it's a string, try to parse it
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.warn('Failed to parse JSON string:', data, error);
      return null;
    }
  }

  return data;
}

/**
 * Get all conversations for the current user
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const conversations = await executeQuery<any[]>(`
      SELECT 
        c.id,
        c.type,
        c.name,
        c.description,
        c.avatar_url,
        c.created_at,
        c.updated_at,
        (
          SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
              'id', u.id,
              'username', u.username,
              'display_name', u.display_name,
              'avatar_url', u.avatar_url,
              'status', u.status
            )
          )
          FROM conversation_participants cp2
          JOIN users u ON cp2.user_id = u.id
          WHERE cp2.conversation_id = c.id AND cp2.left_at IS NULL
        ) as participants,
        (
          SELECT JSON_OBJECT(
            'id', m.id,
            'content', m.content,
            'message_type', m.message_type,
            'sender_id', m.sender_id,
            'sender_username', u.username,
            'sender_display_name', u.display_name,
            'created_at', m.created_at
          )
          FROM messages m
          JOIN users u ON m.sender_id = u.id
          WHERE m.conversation_id = c.id
          ORDER BY m.created_at DESC
          LIMIT 1
        ) as last_message,
        (
          SELECT COUNT(*)
          FROM messages m
          LEFT JOIN message_read_status mrs ON m.id = mrs.message_id AND mrs.user_id = ?
          WHERE m.conversation_id = c.id 
            AND m.sender_id != ?
            AND mrs.id IS NULL
            AND m.deleted_at IS NULL
        ) as unread_count
      FROM conversations c
      JOIN conversation_participants cp ON c.id = cp.conversation_id
      WHERE cp.user_id = ? AND cp.left_at IS NULL
      ORDER BY c.updated_at DESC
    `, [userId, userId, userId]);

    // Parse JSON fields safely
    const parsedConversations = conversations.map(conv => ({
      ...conv,
      participants: safeJsonParse(conv.participants) || [],
      last_message: safeJsonParse(conv.last_message)
    }));

    res.json({
      success: true,
      data: parsedConversations
    });
  } catch (error) {
    console.error('Get conversations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversations',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Get a specific conversation by ID
 */
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const conversationId = parseInt(req.params.id);

    // Check if user is participant in this conversation
    const participation = await executeQuery<ConversationParticipantRow[]>(
      'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversationId, userId]
    );

    if (participation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // Get conversation details
    const conversation = await executeQuery<any[]>(`
      SELECT 
        c.*,
        JSON_ARRAYAGG(
          JSON_OBJECT(
            'id', u.id,
            'username', u.username,
            'display_name', u.display_name,
            'avatar_url', u.avatar_url,
            'status', u.status,
            'role', cp.role,
            'joined_at', cp.joined_at
          )
        ) as participants
      FROM conversations c
      JOIN conversation_participants cp ON c.id = cp.conversation_id
      JOIN users u ON cp.user_id = u.id
      WHERE c.id = ? AND cp.left_at IS NULL
      GROUP BY c.id
    `, [conversationId]);

    if (conversation.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    const result = {
      ...conversation[0],
      participants: safeJsonParse(conversation[0].participants) || []
    };

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversation',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Create a new conversation (direct or group)
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { type, name, description, participant_ids }: {
      type: 'direct' | 'group';
      name?: string;
      description?: string;
      participant_ids: number[];
    } = req.body;

    if (!type || !participant_ids || participant_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Type and participant IDs are required'
      });
    }

    // For direct conversations, ensure only 2 participants (including creator)
    if (type === 'direct') {
      if (participant_ids.length !== 1) {
        return res.status(400).json({
          success: false,
          message: 'Direct conversations must have exactly one other participant'
        });
      }

      // Check if direct conversation already exists
      const existingConversation = await executeQuery<any[]>(`
        SELECT c.id
        FROM conversations c
        JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
        JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
        WHERE c.type = 'direct'
          AND cp1.user_id = ? AND cp1.left_at IS NULL
          AND cp2.user_id = ? AND cp2.left_at IS NULL
          AND (
            SELECT COUNT(*) 
            FROM conversation_participants cp3 
            WHERE cp3.conversation_id = c.id AND cp3.left_at IS NULL
          ) = 2
      `, [userId, participant_ids[0]]);

      if (existingConversation.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'Direct conversation already exists',
          data: { conversation_id: existingConversation[0].id }
        });
      }
    }

    // For group conversations, name is required
    if (type === 'group' && !name) {
      return res.status(400).json({
        success: false,
        message: 'Group conversations must have a name'
      });
    }

    // Create conversation
    const conversationData: CreateConversationData = {
      type,
      name: type === 'group' ? name : undefined,
      description,
      created_by: userId
    };

    const conversationResult = await executeQuery<any>(
      'INSERT INTO conversations (type, name, description, created_by) VALUES (?, ?, ?, ?)',
      [conversationData.type, conversationData.name, conversationData.description, conversationData.created_by]
    );

    const conversationId = conversationResult.insertId;

    // Add creator as participant (admin for groups, member for direct)
    await executeQuery(
      'INSERT INTO conversation_participants (conversation_id, user_id, role) VALUES (?, ?, ?)',
      [conversationId, userId, type === 'group' ? 'admin' : 'member']
    );

    // Add other participants
    for (const participantId of participant_ids) {
      await executeQuery(
        'INSERT INTO conversation_participants (conversation_id, user_id, role) VALUES (?, ?, ?)',
        [conversationId, participantId, 'member']
      );
    }

    // Get the created conversation with participants
    const newConversation = await executeQuery<any[]>(`
      SELECT 
        c.*,
        JSON_ARRAYAGG(
          JSON_OBJECT(
            'id', u.id,
            'username', u.username,
            'display_name', u.display_name,
            'avatar_url', u.avatar_url,
            'status', u.status,
            'role', cp.role
          )
        ) as participants
      FROM conversations c
      JOIN conversation_participants cp ON c.id = cp.conversation_id
      JOIN users u ON cp.user_id = u.id
      WHERE c.id = ? AND cp.left_at IS NULL
      GROUP BY c.id
    `, [conversationId]);

    const result = {
      ...newConversation[0],
      participants: safeJsonParse(newConversation[0].participants) || []
    };

    res.status(201).json({
      success: true,
      data: result,
      message: 'Conversation created successfully'
    });
  } catch (error) {
    console.error('Create conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create conversation',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Add participants to a group conversation
 */
router.post('/:id/participants', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const conversationId = parseInt(req.params.id);
    const { participant_ids }: { participant_ids: number[] } = req.body;

    if (!participant_ids || participant_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Participant IDs are required'
      });
    }

    // Check if user is admin of this group conversation
    const userRole = await executeQuery<ConversationParticipantRow[]>(
      'SELECT role FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversationId, userId]
    );

    if (userRole.length === 0 || userRole[0].role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only admins can add participants to group conversations'
      });
    }

    // Check if conversation is a group
    const conversation = await executeQuery<ConversationRow[]>(
      'SELECT type FROM conversations WHERE id = ?',
      [conversationId]
    );

    if (conversation.length === 0 || conversation[0].type !== 'group') {
      return res.status(400).json({
        success: false,
        message: 'Can only add participants to group conversations'
      });
    }

    // Add participants
    for (const participantId of participant_ids) {
      try {
        await executeQuery(
          'INSERT INTO conversation_participants (conversation_id, user_id, role) VALUES (?, ?, ?)',
          [conversationId, participantId, 'member']
        );
      } catch (error) {
        // Ignore duplicate key errors (user already in conversation)
        console.warn(`User ${participantId} already in conversation ${conversationId}`);
      }
    }

    res.json({
      success: true,
      message: 'Participants added successfully'
    });
  } catch (error) {
    console.error('Add participants error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add participants',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Leave a conversation
 */
router.post('/:id/leave', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const conversationId = parseInt(req.params.id);

    // Update participant record to mark as left
    const result = await executeQuery<any>(
      'UPDATE conversation_participants SET left_at = CURRENT_TIMESTAMP WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversationId, userId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    res.json({
      success: true,
      message: 'Left conversation successfully'
    });
  } catch (error) {
    console.error('Leave conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to leave conversation',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

export function setupConversationRoutes(app: any): void {
  app.use('/api/conversations', router);
}
