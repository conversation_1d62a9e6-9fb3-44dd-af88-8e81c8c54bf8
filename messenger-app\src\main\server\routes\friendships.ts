import { Router, Response } from 'express';
import { executeQuery } from '../../database/config';
import { UserRow, FriendshipRow } from '../../database/models';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';

const router = Router();

/**
 * Send a friend request
 */
router.post('/request', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const currentUserId = req.user!.userId;
    const { username } = req.body;

    if (!username || typeof username !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Username is required'
      });
    }

    // Find the user by username
    const targetUsers = await executeQuery<UserRow[]>(`
      SELECT id, username, display_name
      FROM users
      WHERE username = ?
    `, [username.trim()]);

    if (targetUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const targetUser = targetUsers[0];

    // Can't send friend request to yourself
    if (targetUser.id === currentUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send friend request to yourself'
      });
    }

    // Check if friendship already exists (in either direction)
    const existingFriendships = await executeQuery<FriendshipRow[]>(`
      SELECT id, status, requester_id, addressee_id
      FROM friendships
      WHERE (requester_id = ? AND addressee_id = ?) OR (requester_id = ? AND addressee_id = ?)
    `, [currentUserId, targetUser.id, targetUser.id, currentUserId]);

    if (existingFriendships.length > 0) {
      const friendship = existingFriendships[0];
      
      if (friendship.status === 'accepted') {
        return res.status(400).json({
          success: false,
          message: 'You are already friends with this user'
        });
      } else if (friendship.status === 'pending') {
        if (friendship.requester_id === currentUserId) {
          return res.status(400).json({
            success: false,
            message: 'Friend request already sent'
          });
        } else {
          return res.status(400).json({
            success: false,
            message: 'This user has already sent you a friend request'
          });
        }
      } else if (friendship.status === 'blocked') {
        return res.status(400).json({
          success: false,
          message: 'Cannot send friend request to this user'
        });
      }
    }

    // Create the friend request and auto-accept it for better UX
    await executeQuery(`
      INSERT INTO friendships (requester_id, addressee_id, status)
      VALUES (?, ?, 'accepted')
    `, [currentUserId, targetUser.id]);

    // Create the reverse friendship record for bidirectional relationship
    await executeQuery(`
      INSERT INTO friendships (requester_id, addressee_id, status)
      VALUES (?, ?, 'accepted')
      ON DUPLICATE KEY UPDATE status = 'accepted', updated_at = CURRENT_TIMESTAMP
    `, [targetUser.id, currentUserId]);

    res.json({
      success: true,
      message: `${targetUser.display_name} added to your contacts`
    });

  } catch (error) {
    console.error('Send friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send friend request'
    });
  }
});

/**
 * Get pending friend requests (incoming)
 */
router.get('/requests', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const currentUserId = req.user!.userId;

    const requests = await executeQuery<any[]>(`
      SELECT 
        f.id as friendship_id,
        f.created_at,
        u.id as user_id,
        u.username,
        u.display_name,
        u.avatar_url
      FROM friendships f
      JOIN users u ON f.requester_id = u.id
      WHERE f.addressee_id = ? AND f.status = 'pending'
      ORDER BY f.created_at DESC
    `, [currentUserId]);

    res.json({
      success: true,
      data: requests
    });

  } catch (error) {
    console.error('Get friend requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get friend requests'
    });
  }
});

/**
 * Accept a friend request
 */
router.post('/accept/:friendshipId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const currentUserId = req.user!.userId;
    const friendshipId = parseInt(req.params.friendshipId);

    if (isNaN(friendshipId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid friendship ID'
      });
    }

    // Verify the friendship exists and is pending
    const friendships = await executeQuery<FriendshipRow[]>(`
      SELECT id, requester_id, addressee_id, status
      FROM friendships
      WHERE id = ? AND addressee_id = ? AND status = 'pending'
    `, [friendshipId, currentUserId]);

    if (friendships.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      });
    }

    const friendship = friendships[0];

    // Update the friendship status to accepted
    await executeQuery(`
      UPDATE friendships
      SET status = 'accepted', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [friendshipId]);

    // Create the reverse friendship record for bidirectional relationship
    await executeQuery(`
      INSERT INTO friendships (requester_id, addressee_id, status)
      VALUES (?, ?, 'accepted')
      ON DUPLICATE KEY UPDATE status = 'accepted', updated_at = CURRENT_TIMESTAMP
    `, [currentUserId, friendship.requester_id]);

    res.json({
      success: true,
      message: 'Friend request accepted'
    });

  } catch (error) {
    console.error('Accept friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept friend request'
    });
  }
});

/**
 * Decline a friend request
 */
router.post('/decline/:friendshipId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const currentUserId = req.user!.userId;
    const friendshipId = parseInt(req.params.friendshipId);

    if (isNaN(friendshipId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid friendship ID'
      });
    }

    // Verify the friendship exists and is pending
    const friendships = await executeQuery<FriendshipRow[]>(`
      SELECT id, requester_id, addressee_id, status
      FROM friendships
      WHERE id = ? AND addressee_id = ? AND status = 'pending'
    `, [friendshipId, currentUserId]);

    if (friendships.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      });
    }

    // Delete the friendship record (or update to declined if you want to keep history)
    await executeQuery(`
      DELETE FROM friendships
      WHERE id = ?
    `, [friendshipId]);

    res.json({
      success: true,
      message: 'Friend request declined'
    });

  } catch (error) {
    console.error('Decline friend request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to decline friend request'
    });
  }
});

export function setupFriendshipRoutes(app: any): void {
  app.use('/api/friendships', router);
}
