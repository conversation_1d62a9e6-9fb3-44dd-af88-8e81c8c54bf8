<template>
  <div v-if="replyToMessage" class="reply-indicator" @click="$emit('click-reply')">
    <div class="reply-line"></div>
    <div class="reply-info">
      <div class="reply-header">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 10h10a8 8 0 0 1 8 8v2"></path>
          <path d="m3 10 6 6"></path>
          <path d="m3 10 6-6"></path>
        </svg>
        <span class="reply-author">{{ replyToMessage.sender_display_name }}</span>
      </div>
      <p class="reply-content">{{ truncateText(replyToMessage.content, 80) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ReplyToMessage {
  id: number
  content: string
  sender_username: string
  sender_display_name: string
}

interface Props {
  replyToMessage: ReplyToMessage | null
}

defineProps<Props>()
defineEmits<{
  'click-reply': []
}>()

const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text || ''
  return text.substring(0, maxLength) + '...'
}
</script>

<style scoped>
.reply-indicator {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.reply-indicator:hover {
  background: rgba(0, 0, 0, 0.05);
}

.reply-line {
  width: 3px;
  background: #6366f1;
  border-radius: 2px;
  min-height: 32px;
  margin-top: 2px;
}

.reply-info {
  flex: 1;
  min-width: 0;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
}

.reply-header svg {
  color: #6366f1;
  flex-shrink: 0;
}

.reply-author {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6366f1;
}

.reply-content {
  margin: 0;
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.3;
  word-break: break-word;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .reply-indicator:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .reply-line {
    background: #818cf8;
  }
  
  .reply-header svg {
    color: #818cf8;
  }
  
  .reply-author {
    color: #818cf8;
  }
  
  .reply-content {
    color: #9ca3af;
  }
}
</style>
