<template>
  <div v-if="reactions && reactions.length > 0" class="message-reactions">
    <button
      v-for="reaction in reactions"
      :key="reaction.emoji"
      class="reaction-button"
      :class="{ 'user-reacted': hasUserReacted(reaction) }"
      @click="toggleReaction(reaction.emoji)"
      :title="getReactionTooltip(reaction)"
    >
      <span class="reaction-emoji">{{ reaction.emoji }}</span>
      <span class="reaction-count">{{ reaction.count }}</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue'
import { useAuthStore } from '../../stores/auth'

interface Reaction {
  emoji: string
  count: number
  users: string[]
  user_ids: number[]
}

interface Props {
  reactions: Reaction[]
  messageId: number
}

interface Emits {
  (e: 'toggle-reaction', emoji: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()

const currentUserId = computed(() => authStore.user?.id)

const hasUserReacted = (reaction: Reaction): boolean => {
  return currentUserId.value ? reaction.user_ids.includes(currentUserId.value) : false
}

const toggleReaction = (emoji: string) => {
  emit('toggle-reaction', emoji)
}

const getReactionTooltip = (reaction: Reaction): string => {
  if (reaction.count === 1) {
    return `${reaction.users[0]} reacted with ${reaction.emoji}`
  } else if (reaction.count === 2) {
    return `${reaction.users[0]} and ${reaction.users[1]} reacted with ${reaction.emoji}`
  } else if (reaction.count <= 5) {
    const lastUser = reaction.users[reaction.users.length - 1]
    const otherUsers = reaction.users.slice(0, -1).join(', ')
    return `${otherUsers} and ${lastUser} reacted with ${reaction.emoji}`
  } else {
    const firstUsers = reaction.users.slice(0, 3).join(', ')
    const remainingCount = reaction.count - 3
    return `${firstUsers} and ${remainingCount} others reacted with ${reaction.emoji}`
  }
}
</script>

<style scoped>
.message-reactions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.reaction-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 24px;
}

.reaction-button:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: scale(1.05);
}

.reaction-button.user-reacted {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.reaction-button.user-reacted:hover {
  background: #bfdbfe;
  border-color: #2563eb;
}

.reaction-emoji {
  font-size: 14px;
  line-height: 1;
}

.reaction-count {
  font-weight: 500;
  font-size: 11px;
  color: #6b7280;
}

.reaction-button.user-reacted .reaction-count {
  color: #1d4ed8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .reaction-button {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .reaction-button:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .reaction-button.user-reacted {
    background: #1e3a8a;
    border-color: #3b82f6;
    color: #93c5fd;
  }
  
  .reaction-button.user-reacted:hover {
    background: #1e40af;
    border-color: #60a5fa;
  }
  
  .reaction-count {
    color: #9ca3af;
  }
  
  .reaction-button.user-reacted .reaction-count {
    color: #93c5fd;
  }
}
</style>
