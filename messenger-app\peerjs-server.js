const { PeerServer } = require('peer');

const peerServer = PeerServer({
  port: 9000,
  path: '/peerjs',
  debug: true,
  allow_discovery: true
});

console.log('🔗 PeerJS server running on port 9000');

peerServer.on('connection', (client) => {
  console.log(`📱 Client connected: ${client.getId()}`);
});

peerServer.on('disconnect', (client) => {
  console.log(`📱 Client disconnected: ${client.getId()}`);
});
