import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { executeQuery } from '../../database/config';
import {
  UserRow,
  ConversationParticipantRow,
  SocketUser,
  SocketMessage,
  CallOffer,
  CallAnswer,
  IceCandidate
} from '../../database/models';

// Helper function to get message attachments
async function getMessageAttachments(messageId: number) {
  const attachments = await executeQuery<any[]>(
    'SELECT id, original_name, filename, mimetype, size, url, thumbnail_url, is_image, uploaded_at FROM message_attachments WHERE message_id = ?',
    [messageId]
  );

  return attachments.map(att => ({
    id: att.id,
    originalName: att.original_name,
    filename: att.filename,
    mimetype: att.mimetype,
    size: att.size,
    url: att.url,
    thumbnailUrl: att.thumbnail_url,
    isImage: att.is_image,
    uploadedAt: att.uploaded_at
  }));
}

// Store active user connections
const activeUsers = new Map<number, string>(); // userId -> socketId
const userSockets = new Map<string, number>(); // socketId -> userId

/**
 * Authenticate socket connection using JWT token
 */
async function authenticateSocket(socket: Socket): Promise<UserRow | null> {
  try {
    const token = socket.handshake.auth.token;
    if (!token) {
      return null;
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    const users = await executeQuery<UserRow[]>(
      'SELECT * FROM users WHERE id = ?',
      [decoded.userId]
    );

    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error('Socket authentication error:', error);
    return null;
  }
}

/**
 * Get user's conversation IDs for room management
 */
async function getUserConversations(userId: number): Promise<number[]> {
  try {
    const conversations = await executeQuery<any[]>(
      'SELECT conversation_id FROM conversation_participants WHERE user_id = ? AND left_at IS NULL',
      [userId]
    );
    return conversations.map(c => c.conversation_id);
  } catch (error) {
    console.error('Get user conversations error:', error);
    return [];
  }
}

/**
 * Broadcast user status change to relevant conversations
 */
async function broadcastUserStatus(io: SocketIOServer, userId: number, status: string): Promise<void> {
  try {
    const conversationIds = await getUserConversations(userId);
    
    // Get user info
    const users = await executeQuery<UserRow[]>(
      'SELECT id, username, display_name, avatar_url, status FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) return;

    const userInfo: SocketUser = {
      id: users[0].id,
      username: users[0].username,
      display_name: users[0].display_name,
      avatar_url: users[0].avatar_url,
      status: status
    };

    // Broadcast to all conversation rooms
    conversationIds.forEach(conversationId => {
      io.to(`conversation_${conversationId}`).emit('user_status_changed', userInfo);
    });
  } catch (error) {
    console.error('Broadcast user status error:', error);
  }
}

/**
 * Setup Socket.IO event handlers
 */
export function setupSocketHandlers(io: SocketIOServer): void {
  io.on('connection', async (socket: Socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Authenticate user
    const user = await authenticateSocket(socket);
    if (!user) {
      console.log(`Unauthenticated socket connection: ${socket.id}`);
      socket.emit('auth_error', { message: 'Authentication required' });
      socket.disconnect();
      return;
    }

    console.log(`User authenticated: ${user.username} (${user.id})`);

    // Store user connection
    activeUsers.set(user.id, socket.id);
    userSockets.set(socket.id, user.id);

    // Update user status to online
    await executeQuery(
      'UPDATE users SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?',
      ['online', user.id]
    );

    // Join user's conversation rooms
    const conversationIds = await getUserConversations(user.id);
    conversationIds.forEach(conversationId => {
      socket.join(`conversation_${conversationId}`);
    });

    // Broadcast user online status
    await broadcastUserStatus(io, user.id, 'online');

    // Send user info to client
    socket.emit('authenticated', {
      user: {
        id: user.id,
        username: user.username,
        display_name: user.display_name,
        avatar_url: user.avatar_url,
        status: user.status
      }
    });

    // Handle sending messages
    socket.on('send_message', async (data: {
      conversation_id: number;
      content: string;
      message_type?: string;
      attachments?: any[];
      reply_to_id?: number;
    }) => {
      try {
        // Verify user is participant in conversation
        const participation = await executeQuery<ConversationParticipantRow[]>(
          'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
          [data.conversation_id, user.id]
        );

        if (participation.length === 0) {
          socket.emit('message_error', { message: 'Access denied to conversation' });
          return;
        }

        // Create message in database
        const result = await executeQuery<any>(
          'INSERT INTO messages (conversation_id, sender_id, content, message_type, reply_to_id) VALUES (?, ?, ?, ?, ?)',
          [data.conversation_id, user.id, data.content, data.message_type || 'text', data.reply_to_id]
        );

        const messageId = result.insertId;

        // Insert attachments if provided
        if (data.attachments && data.attachments.length > 0) {
          for (const attachment of data.attachments) {
            await executeQuery(
              'INSERT INTO message_attachments (id, message_id, original_name, filename, mimetype, size, url, thumbnail_url, is_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
              [
                attachment.id,
                messageId,
                attachment.originalName,
                attachment.filename,
                attachment.mimetype,
                attachment.size,
                attachment.url,
                attachment.thumbnailUrl || null,
                attachment.isImage
              ]
            );
          }
        }

        // Update conversation timestamp
        await executeQuery(
          'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [data.conversation_id]
        );

        // Get the created message with sender info
        const messages = await executeQuery<any[]>(`
          SELECT 
            m.id,
            m.conversation_id,
            m.content,
            m.message_type,
            m.reply_to_id,
            m.created_at,
            u.id as sender_id,
            u.username as sender_username,
            u.display_name as sender_display_name,
            u.avatar_url as sender_avatar_url
          FROM messages m
          JOIN users u ON m.sender_id = u.id
          WHERE m.id = ?
        `, [result.insertId]);

        const message = messages[0];

        // Get attachments for the message
        const messageAttachments = await getMessageAttachments(messageId);

        const socketMessage: SocketMessage = {
          id: message.id,
          conversation_id: message.conversation_id,
          sender: {
            id: message.sender_id,
            username: message.sender_username,
            display_name: message.sender_display_name,
            avatar_url: message.sender_avatar_url,
            status: 'online'
          },
          content: message.content,
          message_type: message.message_type,
          attachments: messageAttachments,
          reply_to_id: message.reply_to_id,
          created_at: message.created_at
        };

        // Broadcast message to conversation room
        io.to(`conversation_${data.conversation_id}`).emit('new_message', socketMessage);

      } catch (error) {
        console.error('Send message error:', error);
        socket.emit('message_error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { conversation_id: number }) => {
      socket.to(`conversation_${data.conversation_id}`).emit('user_typing', {
        user_id: user.id,
        username: user.username,
        conversation_id: data.conversation_id
      });
    });

    socket.on('typing_stop', (data: { conversation_id: number }) => {
      socket.to(`conversation_${data.conversation_id}`).emit('user_stopped_typing', {
        user_id: user.id,
        username: user.username,
        conversation_id: data.conversation_id
      });
    });

    // Handle message reactions
    socket.on('add_reaction', async (data: { message_id: number; emoji: string }) => {
      try {
        // Make API call to add reaction
        const response = await fetch(`http://localhost:3001/api/messages/${data.message_id}/reactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${socket.handshake.auth.token}`
          },
          body: JSON.stringify({ emoji: data.emoji })
        });

        if (response.ok) {
          const result = await response.json();

          // Get message to find conversation_id
          const messages = await executeQuery<any[]>(
            'SELECT conversation_id FROM messages WHERE id = ?',
            [data.message_id]
          );

          if (messages.length > 0) {
            // Broadcast reaction update to conversation room
            io.to(`conversation_${messages[0].conversation_id}`).emit('reaction_updated', {
              message_id: data.message_id,
              action: result.data.action,
              reactions: result.data.reactions,
              user_id: user.id,
              emoji: data.emoji
            });
          }
        }
      } catch (error) {
        console.error('Add reaction error:', error);
        socket.emit('reaction_error', { message: 'Failed to add reaction' });
      }
    });

    // Handle joining/leaving conversations
    socket.on('join_conversation', (data: { conversation_id: number }) => {
      socket.join(`conversation_${data.conversation_id}`);
    });

    socket.on('leave_conversation', (data: { conversation_id: number }) => {
      socket.leave(`conversation_${data.conversation_id}`);
    });

    // Handle WebRTC signaling for video/audio calls
    socket.on('call_offer', async (data: CallOffer) => {
      try {
        // Create call record in database
        const callResult = await executeQuery<any>(
          'INSERT INTO calls (conversation_id, caller_id, call_type, status) VALUES (?, ?, ?, ?)',
          [data.call_id, user.id, data.call_type, 'initiated']
        );

        const callId = callResult.insertId;

        // Broadcast call offer to conversation participants (except caller)
        socket.to(`conversation_${data.call_id}`).emit('incoming_call', {
          call_id: callId,
          caller: data.caller,
          call_type: data.call_type,
          offer: data.offer
        });

      } catch (error) {
        console.error('Call offer error:', error);
        socket.emit('call_error', { message: 'Failed to initiate call' });
      }
    });

    socket.on('call_answer', (data: CallAnswer) => {
      // Forward answer to caller
      socket.to(`conversation_${data.call_id}`).emit('call_answered', {
        call_id: data.call_id,
        answer: data.answer
      });
    });

    socket.on('call_decline', (data: { call_id: number }) => {
      socket.to(`conversation_${data.call_id}`).emit('call_declined', {
        call_id: data.call_id
      });
    });

    socket.on('call_end', async (data: { call_id: number }) => {
      try {
        // Update call status in database
        await executeQuery(
          'UPDATE calls SET status = ?, ended_at = CURRENT_TIMESTAMP WHERE id = ?',
          ['ended', data.call_id]
        );

        // Broadcast call end to all participants
        socket.to(`conversation_${data.call_id}`).emit('call_ended', {
          call_id: data.call_id
        });

      } catch (error) {
        console.error('Call end error:', error);
      }
    });

    socket.on('ice_candidate', (data: IceCandidate) => {
      // Forward ICE candidate to other participants
      socket.to(`conversation_${data.call_id}`).emit('ice_candidate', {
        call_id: data.call_id,
        candidate: data.candidate
      });
    });

    // Handle user status changes
    socket.on('status_change', async (data: { status: string }) => {
      try {
        if (!['online', 'away', 'busy', 'offline'].includes(data.status)) {
          return;
        }

        await executeQuery(
          'UPDATE users SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?',
          [data.status, user.id]
        );

        await broadcastUserStatus(io, user.id, data.status);

      } catch (error) {
        console.error('Status change error:', error);
      }
    });

    // Handle disconnect
    socket.on('disconnect', async () => {
      console.log(`Socket disconnected: ${socket.id} (User: ${user.username})`);

      // Remove from active users
      activeUsers.delete(user.id);
      userSockets.delete(socket.id);

      // Update user status to offline
      await executeQuery(
        'UPDATE users SET status = ?, last_seen = CURRENT_TIMESTAMP WHERE id = ?',
        ['offline', user.id]
      );

      // Broadcast user offline status
      await broadcastUserStatus(io, user.id, 'offline');
    });

    // Handle errors
    socket.on('error', (error) => {
      console.error(`Socket error for user ${user.username}:`, error);
    });
  });

  // Handle server-side errors
  io.on('error', (error) => {
    console.error('Socket.IO server error:', error);
  });

  console.log('✅ Socket.IO handlers setup completed');
}
