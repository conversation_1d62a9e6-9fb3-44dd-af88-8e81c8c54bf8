<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useChatStore } from '../../stores/chat'
import { useAuthStore } from '../../stores/auth'
import type { Friend } from '../../stores/chat'

const chatStore = useChatStore()
const authStore = useAuthStore()

const sortedFriends = computed(() => {
  return [...chatStore.friends].sort((a, b) => {
    // Sort by online status first, then by display name
    if (a.status === 'online' && b.status !== 'online') return -1
    if (b.status === 'online' && a.status !== 'online') return 1
    return a.display_name.localeCompare(b.display_name)
  })
})

const getFriendInitials = (friend: Friend): string => {
  return friend.display_name
    .split(' ')
    .map(name => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'online': return '#10b981'
    case 'away': return '#f59e0b'
    case 'busy': return '#ef4444'
    default: return '#6b7280'
  }
}

const formatLastSeen = (lastSeen: string): string => {
  const now = new Date()
  const lastSeenDate = new Date(lastSeen)
  const diffInMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`

  return lastSeenDate.toLocaleDateString()
}

const startConversationWithFriend = async (friend: Friend) => {
  try {
    // Check if a direct conversation already exists with this friend
    const existingConversation = chatStore.conversations.find(conv => 
      conv.type === 'direct' && 
      conv.participants.some(p => p.id === friend.id)
    )

    if (existingConversation) {
      // Select the existing conversation
      await chatStore.setActiveConversation(existingConversation)
    } else {
      // Create a new direct conversation
      const newConversation = await chatStore.createConversation('direct', [friend.id])
      if (newConversation) {
        await chatStore.setActiveConversation(newConversation)
      }
    }
  } catch (error) {
    console.error('Failed to start conversation with friend:', error)
  }
}

// Load friends when component mounts
onMounted(async () => {
  if (authStore.isAuthenticated && chatStore.friends.length === 0) {
    await chatStore.loadFriends()
  }
})
</script>

<template>
  <div class="friends-list">
    <!-- Friends List -->
    <div class="friends-container">
      <div v-if="chatStore.isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading friends...</p>
      </div>

      <div v-else-if="sortedFriends.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
            <circle cx="9" cy="7" r="4"/>
            <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
          </svg>
        </div>
        <p class="empty-text">No friends yet</p>
        <p class="empty-subtext">Add friends to start chatting</p>
      </div>

      <div v-else class="friends-list-container">
        <div
          v-for="friend in sortedFriends"
          :key="friend.id"
          class="friend-item"
          @click="startConversationWithFriend(friend)"
        >
          <!-- Avatar -->
          <div class="friend-avatar">
            <img 
              v-if="friend.avatar_url"
              :src="friend.avatar_url"
              :alt="friend.display_name"
              class="avatar-image"
            />
            <div v-else class="avatar-placeholder">
              {{ getFriendInitials(friend) }}
            </div>
            
            <!-- Online status indicator -->
            <div 
              class="status-indicator"
              :style="{ backgroundColor: getStatusColor(friend.status) }"
              :title="friend.status"
            ></div>
          </div>

          <!-- Friend Info -->
          <div class="friend-info">
            <div class="friend-name">{{ friend.display_name }}</div>
            <div class="friend-status">
              <span class="status-text">{{ friend.status }}</span>
              <span v-if="friend.last_seen && friend.status === 'offline'" class="last-seen">
                Last seen {{ formatLastSeen(friend.last_seen) }}
              </span>
            </div>
          </div>

          <!-- Action indicator -->
          <div class="friend-action">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.friends-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.friends-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 8px;
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  margin-bottom: 16px;
  color: var(--text-secondary);
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.empty-subtext {
  font-size: 14px;
  color: var(--text-secondary);
}

.friends-list-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.friend-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.friend-item:hover {
  background: var(--bg-secondary);
}

.friend-avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.avatar-image {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
}

.friend-info {
  flex: 1;
  min-width: 0;
}

.friend-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.friend-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.status-text {
  color: var(--text-secondary);
  text-transform: capitalize;
}

.last-seen {
  color: var(--text-tertiary);
  font-size: 11px;
}

.friend-action {
  color: var(--text-secondary);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.friend-item:hover .friend-action {
  opacity: 1;
}
</style>
