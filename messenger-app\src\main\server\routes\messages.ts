import { Router, Response } from 'express';
import { executeQuery } from '../../database/config';
import {
  MessageRow,
  CreateMessageData,
  ConversationParticipantRow
} from '../../database/models';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { v4 as uuidv4 } from 'uuid';

const router = Router();

/**
 * Safely parse JSON data that might already be an object
 */
function safeJsonParse(data: any): any {
  if (data === null || data === undefined) {
    return null;
  }

  // If it's already an object or array, return as-is
  if (typeof data === 'object') {
    return data;
  }

  // If it's a string, try to parse it
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.warn('Failed to parse JSON string:', data, error);
      return null;
    }
  }

  return data;
}

// Helper function to get message attachments
async function getMessageAttachments(messageId: number) {
  const attachments = await executeQuery<any[]>(
    'SELECT id, original_name, filename, mimetype, size, url, thumbnail_url, is_image, uploaded_at FROM message_attachments WHERE message_id = ?',
    [messageId]
  );

  return attachments.map(att => ({
    id: att.id,
    originalName: att.original_name,
    filename: att.filename,
    mimetype: att.mimetype,
    size: att.size,
    url: att.url,
    thumbnailUrl: att.thumbnail_url,
    isImage: att.is_image,
    uploadedAt: att.uploaded_at
  }));
}

// Helper function to get message reactions with counts and user info
async function getMessageReactions(messageId: number): Promise<any[]> {
  const reactions = await executeQuery<any[]>(`
    SELECT
      mr.emoji,
      COUNT(*) as count,
      GROUP_CONCAT(u.display_name) as user_names,
      GROUP_CONCAT(u.id) as user_ids
    FROM message_reactions mr
    JOIN users u ON mr.user_id = u.id
    WHERE mr.message_id = ?
    GROUP BY mr.emoji
    ORDER BY COUNT(*) DESC, mr.created_at ASC
  `, [messageId]);

  return reactions.map(reaction => ({
    emoji: reaction.emoji,
    count: reaction.count,
    users: reaction.user_names ? reaction.user_names.split(',') : [],
    user_ids: reaction.user_ids ? reaction.user_ids.split(',').map((id: string) => parseInt(id)) : []
  }));
}

/**
 * Get messages for a conversation with pagination
 */
router.get('/conversation/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const conversationId = parseInt(req.params.id);
    const { limit = 50, before_id } = req.query;

    // Check if user is participant in this conversation
    const participation = await executeQuery<ConversationParticipantRow[]>(
      'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversationId, userId]
    );

    if (participation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // Build query with optional before_id for pagination
    let query = `
      SELECT 
        m.id,
        m.conversation_id,
        m.sender_id,
        m.content,
        m.message_type,
        m.file_url,
        m.file_name,
        m.file_size,
        m.reply_to_id,
        m.edited_at,
        m.created_at,
        u.username as sender_username,
        u.display_name as sender_display_name,
        u.avatar_url as sender_avatar_url,
        (
          SELECT JSON_OBJECT(
            'id', rm.id,
            'content', rm.content,
            'sender_username', ru.username,
            'sender_display_name', ru.display_name
          )
          FROM messages rm
          JOIN users ru ON rm.sender_id = ru.id
          WHERE rm.id = m.reply_to_id
        ) as reply_to_message
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      WHERE m.conversation_id = ? AND m.deleted_at IS NULL
    `;

    const params: any[] = [conversationId];

    if (before_id) {
      query += ' AND m.id < ?';
      params.push(parseInt(before_id as string));
    }

    query += ' ORDER BY m.created_at DESC LIMIT ?';
    params.push(parseInt(limit as string));

    const messages = await executeQuery<any[]>(query, params);

    // Parse reply_to_message JSON and get attachments and reactions for each message
    const parsedMessages = await Promise.all(
      messages.map(async (msg) => {
        const attachments = await getMessageAttachments(msg.id);
        const reactions = await getMessageReactions(msg.id);
        return {
          ...msg,
          attachments,
          reactions,
          reply_to_message: safeJsonParse(msg.reply_to_message)
        };
      })
    );

    res.json({
      success: true,
      data: parsedMessages.reverse() // Reverse to show oldest first
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get messages',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Send a new message
 */
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const {
      conversation_id,
      content,
      message_type = 'text',
      file_url,
      file_name,
      file_size,
      attachments,
      reply_to_id
    }: CreateMessageData = req.body;

    if (!conversation_id || (!content && (!attachments || attachments.length === 0))) {
      return res.status(400).json({
        success: false,
        message: 'Conversation ID and either content or attachments are required'
      });
    }

    // Check if user is participant in this conversation
    const participation = await executeQuery<ConversationParticipantRow[]>(
      'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversation_id, userId]
    );

    if (participation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // If replying to a message, verify it exists in the same conversation
    if (reply_to_id) {
      const replyMessage = await executeQuery<MessageRow[]>(
        'SELECT id FROM messages WHERE id = ? AND conversation_id = ? AND deleted_at IS NULL',
        [reply_to_id, conversation_id]
      );

      if (replyMessage.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Reply message not found in this conversation'
        });
      }
    }

    // Create message
    const messageData: CreateMessageData = {
      conversation_id,
      sender_id: userId,
      content,
      message_type,
      file_url,
      file_name,
      file_size,
      reply_to_id
    };

    const result = await executeQuery<any>(
      'INSERT INTO messages (conversation_id, sender_id, content, message_type, file_url, file_name, file_size, reply_to_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [
        messageData.conversation_id,
        messageData.sender_id,
        messageData.content,
        messageData.message_type,
        messageData.file_url,
        messageData.file_name,
        messageData.file_size,
        messageData.reply_to_id
      ]
    );

    const messageId = result.insertId;

    // Insert attachments if provided
    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        await executeQuery(
          'INSERT INTO message_attachments (id, message_id, original_name, filename, mimetype, size, url, thumbnail_url, is_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            attachment.id,
            messageId,
            attachment.originalName,
            attachment.filename,
            attachment.mimetype,
            attachment.size,
            attachment.url,
            attachment.thumbnailUrl || null,
            attachment.isImage
          ]
        );
      }
    }

    // Update conversation's updated_at timestamp
    await executeQuery(
      'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [conversation_id]
    );

    // Get the created message with sender info
    const newMessage = await executeQuery<any[]>(`
      SELECT 
        m.id,
        m.conversation_id,
        m.sender_id,
        m.content,
        m.message_type,
        m.file_url,
        m.file_name,
        m.file_size,
        m.reply_to_id,
        m.created_at,
        u.username as sender_username,
        u.display_name as sender_display_name,
        u.avatar_url as sender_avatar_url,
        (
          SELECT JSON_OBJECT(
            'id', rm.id,
            'content', rm.content,
            'sender_username', ru.username,
            'sender_display_name', ru.display_name
          )
          FROM messages rm
          JOIN users ru ON rm.sender_id = ru.id
          WHERE rm.id = m.reply_to_id
        ) as reply_to_message
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      WHERE m.id = ?
    `, [messageId]);

    // Get attachments and reactions for the message
    const messageAttachments = await getMessageAttachments(messageId);
    const messageReactions = await getMessageReactions(messageId);

    const message = {
      ...newMessage[0],
      attachments: messageAttachments,
      reactions: messageReactions,
      reply_to_message: safeJsonParse(newMessage[0].reply_to_message)
    };

    res.status(201).json({
      success: true,
      data: message,
      message: 'Message sent successfully'
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Edit a message
 */
router.put('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const messageId = parseInt(req.params.id);
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: 'Content is required'
      });
    }

    // Check if message exists and user is the sender
    const message = await executeQuery<MessageRow[]>(
      'SELECT * FROM messages WHERE id = ? AND sender_id = ? AND deleted_at IS NULL',
      [messageId, userId]
    );

    if (message.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you are not the sender'
      });
    }

    // Update message
    await executeQuery(
      'UPDATE messages SET content = ?, edited_at = CURRENT_TIMESTAMP WHERE id = ?',
      [content, messageId]
    );

    // Get updated message
    const updatedMessage = await executeQuery<any[]>(`
      SELECT 
        m.*,
        u.username as sender_username,
        u.display_name as sender_display_name,
        u.avatar_url as sender_avatar_url
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      WHERE m.id = ?
    `, [messageId]);

    res.json({
      success: true,
      data: updatedMessage[0],
      message: 'Message updated successfully'
    });
  } catch (error) {
    console.error('Edit message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to edit message',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Delete a message (soft delete)
 */
router.delete('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const messageId = parseInt(req.params.id);

    // Check if message exists and user is the sender
    const message = await executeQuery<MessageRow[]>(
      'SELECT * FROM messages WHERE id = ? AND sender_id = ? AND deleted_at IS NULL',
      [messageId, userId]
    );

    if (message.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found or you are not the sender'
      });
    }

    // Soft delete message
    await executeQuery(
      'UPDATE messages SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?',
      [messageId]
    );

    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete message',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Mark messages as read
 */
router.post('/read', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { message_ids }: { message_ids: number[] } = req.body;

    if (!message_ids || message_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Message IDs are required'
      });
    }

    // Mark messages as read (insert or ignore duplicates)
    for (const messageId of message_ids) {
      try {
        await executeQuery(
          'INSERT INTO message_read_status (message_id, user_id) VALUES (?, ?)',
          [messageId, userId]
        );
      } catch (error) {
        // Ignore duplicate key errors (already marked as read)
        console.warn(`Message ${messageId} already marked as read by user ${userId}`);
      }
    }

    res.json({
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Mark messages as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Search messages in a conversation
 */
router.get('/search/:conversationId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const conversationId = parseInt(req.params.conversationId);
    const { query, limit = 20 } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    // Check if user is participant in this conversation
    const participation = await executeQuery<ConversationParticipantRow[]>(
      'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [conversationId, userId]
    );

    if (participation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // Search messages
    const messages = await executeQuery<any[]>(`
      SELECT 
        m.id,
        m.conversation_id,
        m.content,
        m.message_type,
        m.created_at,
        u.username as sender_username,
        u.display_name as sender_display_name,
        u.avatar_url as sender_avatar_url
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      WHERE m.conversation_id = ? 
        AND m.content LIKE ?
        AND m.deleted_at IS NULL
      ORDER BY m.created_at DESC
      LIMIT ?
    `, [conversationId, `%${query}%`, parseInt(limit as string)]);

    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Search messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search messages',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Add reaction to a message
 */
router.post('/:id/reactions', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const messageId = parseInt(req.params.id);
    const { emoji } = req.body;

    if (!emoji) {
      return res.status(400).json({
        success: false,
        message: 'Emoji is required'
      });
    }

    // Check if message exists
    const message = await executeQuery<MessageRow[]>(
      'SELECT * FROM messages WHERE id = ? AND deleted_at IS NULL',
      [messageId]
    );

    if (message.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // Check if user has access to this conversation
    const participation = await executeQuery<ConversationParticipantRow[]>(
      'SELECT * FROM conversation_participants WHERE conversation_id = ? AND user_id = ? AND left_at IS NULL',
      [message[0].conversation_id, userId]
    );

    if (participation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this conversation'
      });
    }

    // Check if reaction already exists (toggle behavior)
    const existingReaction = await executeQuery<any[]>(
      'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ? AND emoji = ?',
      [messageId, userId, emoji]
    );

    if (existingReaction.length > 0) {
      // Remove existing reaction
      await executeQuery(
        'DELETE FROM message_reactions WHERE message_id = ? AND user_id = ? AND emoji = ?',
        [messageId, userId, emoji]
      );

      // Get updated reaction counts
      const reactions = await getMessageReactions(messageId);

      res.json({
        success: true,
        data: { action: 'removed', reactions },
        message: 'Reaction removed successfully'
      });
    } else {
      // Add new reaction
      const reactionId = uuidv4();
      await executeQuery(
        'INSERT INTO message_reactions (id, message_id, user_id, emoji) VALUES (?, ?, ?, ?)',
        [reactionId, messageId, userId, emoji]
      );

      // Get updated reaction counts
      const reactions = await getMessageReactions(messageId);

      res.json({
        success: true,
        data: { action: 'added', reactions },
        message: 'Reaction added successfully'
      });
    }
  } catch (error) {
    console.error('Add reaction error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add reaction',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Get reactions for a message
 */
router.get('/:id/reactions', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const messageId = parseInt(req.params.id);

    // Check if message exists
    const message = await executeQuery<MessageRow[]>(
      'SELECT * FROM messages WHERE id = ? AND deleted_at IS NULL',
      [messageId]
    );

    if (message.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    const reactions = await getMessageReactions(messageId);

    res.json({
      success: true,
      data: reactions
    });
  } catch (error) {
    console.error('Get reactions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get reactions',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

export function setupMessageRoutes(app: any): void {
  app.use('/api/messages', router);
}
