/* Theme-Specific Animations and Effects */

/* Enhanced Theme Transitions */
.theme-transitioning {
  transition: none !important;
}

.theme-transitioning * {
  transition: none !important;
}

/* Smooth theme switching animation */
body {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button hover animations with theme awareness */
.button-primary {
  position: relative;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.button-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.button-primary:hover::before {
  left: 100%;
}

/* Message bubble animations */
.message-bubble {
  animation: messageSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Theme-aware focus states */
.input-focus {
  position: relative;
}

.input-focus::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--color-primary-600);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.input-focus:focus-within::after {
  width: 100%;
}

/* Conversation item hover effects */
.conversation-item {
  position: relative;
  overflow: hidden;
}

.conversation-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--sidebar-item-hover),
    transparent
  );
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.5;
}

.conversation-item:hover::before {
  left: 100%;
}

/* Theme toggle animation */
.theme-toggle {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle:hover {
  transform: scale(1.1);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* Loading animations with theme colors */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Theme-aware shadows */
.elevated-surface {
  box-shadow: var(--shadow-lg);
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.elevated-surface:hover {
  box-shadow: var(--shadow-xl);
}

/* Notification slide-in animation */
.notification-enter {
  animation: notificationSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes notificationSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Theme switching particle effect */
.theme-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary-600);
  border-radius: 50%;
  animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(0);
  }
}

/* Sidebar collapse animation */
.sidebar-collapse {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Chat message typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: var(--color-text-tertiary);
  border-radius: 50%;
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Theme-aware scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-secondary);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
