const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateReactionsTable() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'messenger_app'
    });

    console.log('Connected to database');

    // Create message_reactions table
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS message_reactions (
        id VARCHAR(36) PRIMARY KEY,
        message_id INT NOT NULL,
        user_id INT NOT NULL,
        emoji VARCHAR(10) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_reaction (message_id, user_id, emoji),
        INDEX idx_message_reactions_message_id (message_id),
        INDEX idx_message_reactions_user_id (user_id)
      )
    `;

    await connection.execute(createTableQuery);
    console.log('✅ message_reactions table created successfully');

    // Check if table exists and show structure
    const [rows] = await connection.execute('DESCRIBE message_reactions');
    console.log('Table structure:');
    console.table(rows);

  } catch (error) {
    console.error('❌ Error updating database:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

updateReactionsTable();
