<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useChatStore } from '../../stores/chat'
import ConversationList from './ConversationList.vue'
import FriendsList from './FriendsList.vue'
import ChatWindow from './ChatWindow.vue'
import UserProfile from './UserProfile.vue'
import NewConversationModal from './NewConversationModal.vue'
import AddContactModal from './AddContactModal.vue'

const authStore = useAuthStore()
const chatStore = useChatStore()

const showUserProfile = ref(false)
const showAddContactModal = ref(false)
const isLoading = ref(true)

onMounted(async () => {
  try {
    // Initialize chat data
    await chatStore.loadConversations()
    await chatStore.loadFriends()
    
    // Setup socket event listeners
    if (authStore.socket) {
      setupSocketListeners()
    }
  } catch (error) {
    console.error('Failed to initialize chat:', error)
  } finally {
    isLoading.value = false
  }
})

onUnmounted(() => {
  // Clean up socket listeners
  if (authStore.socket) {
    authStore.socket.off('new_message')
    authStore.socket.off('user_status_changed')
    authStore.socket.off('user_typing')
    authStore.socket.off('user_stopped_typing')
  }
})

const setupSocketListeners = () => {
  const socket = authStore.socket!

  // Handle new messages
  socket.on('new_message', (message) => {
    chatStore.handleNewMessage(message)
  })

  // Handle user status changes
  socket.on('user_status_changed', (user) => {
    chatStore.updateUserStatus(user)
  })

  // Handle typing indicators
  socket.on('user_typing', (data) => {
    chatStore.setUserTyping(data.conversation_id, data.user_id, true)
  })

  socket.on('user_stopped_typing', (data) => {
    chatStore.setUserTyping(data.conversation_id, data.user_id, false)
  })

  // Handle call events
  socket.on('incoming_call', (data) => {
    chatStore.handleIncomingCall(data)
  })

  socket.on('call_answered', (data) => {
    chatStore.handleCallAnswered(data)
  })

  socket.on('call_declined', (data) => {
    chatStore.handleCallDeclined(data)
  })

  socket.on('call_ended', (data) => {
    chatStore.handleCallEnded(data)
  })

  // Handle reaction updates
  socket.on('reaction_updated', (data) => {
    chatStore.handleReactionUpdate(data)
  })
}

const handleLogout = async () => {
  await authStore.logout()
}

const toggleUserProfile = () => {
  showUserProfile.value = !showUserProfile.value
}
</script>

<template>
  <div class="chat-layout">
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner large"></div>
      <p class="loading-text">Loading your conversations...</p>
    </div>

    <!-- Main Chat Interface -->
    <div v-else class="chat-container">
      <!-- Sidebar -->
      <div class="sidebar">
        <!-- User Header -->
        <div class="user-header">
          <div class="user-info" @click="toggleUserProfile">
            <div class="user-avatar">
              <img 
                v-if="authStore.user?.avatar_url" 
                :src="authStore.user.avatar_url" 
                :alt="authStore.user.display_name"
                class="avatar-image"
              />
              <div v-else class="avatar-placeholder">
                {{ authStore.user?.display_name?.charAt(0)?.toUpperCase() || 'U' }}
              </div>
            </div>
            <div class="user-details">
              <h3 class="user-name">{{ authStore.user?.display_name }}</h3>
              <p class="user-status">{{ authStore.user?.status }}</p>
            </div>
          </div>
          
          <div class="user-actions">
            <button
              class="action-button"
              @click="showAddContactModal = true"
              title="Add Friend"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <line x1="19" y1="8" x2="19" y2="14"/>
                <line x1="22" y1="11" x2="16" y2="11"/>
              </svg>
            </button>

            <button
              class="action-button"
              @click="toggleUserProfile"
              title="Profile Settings"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
              </svg>
            </button>

            <button
              class="action-button"
              @click="handleLogout"
              title="Logout"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content">
          <!-- Conversations Section -->
          <div class="sidebar-section">
            <div class="section-header">
              <h3 class="section-title">Recent Conversations</h3>
            </div>
            <ConversationList />
          </div>

          <!-- Friends Section -->
          <div class="sidebar-section">
            <div class="section-header">
              <h3 class="section-title">Friends</h3>
            </div>
            <FriendsList />
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="main-content">
        <!-- Chat Window -->
        <ChatWindow v-if="chatStore.activeConversation" />
        
        <!-- Welcome Screen -->
        <div v-else class="welcome-screen">
          <div class="welcome-content">
            <div class="welcome-icon">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
              </svg>
            </div>
            <h2 class="welcome-title">Welcome to Messenger</h2>
            <p class="welcome-subtitle">
              Select a conversation to start messaging, or create a new one to connect with friends.
            </p>
            <button 
              class="new-conversation-button"
              @click="chatStore.showNewConversationModal = true"
            >
              Start New Conversation
            </button>
          </div>
        </div>
      </div>

      <!-- User Profile Modal -->
      <UserProfile
        v-if="showUserProfile"
        @close="showUserProfile = false"
      />

      <!-- New Conversation Modal -->
      <NewConversationModal
        v-if="chatStore.showNewConversationModal"
        @close="chatStore.showNewConversationModal = false"
      />

      <!-- Add Friend Modal -->
      <AddContactModal
        :is-visible="showAddContactModal"
        @close="showAddContactModal = false"
      />
    </div>
  </div>
</template>

<style scoped>
.chat-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: var(--color-surface-secondary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.large {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: 1rem;
  margin: 0;
}

.chat-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 320px;
  background: var(--sidebar-background);
  border-right: 1px solid var(--sidebar-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.user-header {
  padding: 16px;
  border-bottom: 1px solid var(--sidebar-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--sidebar-background);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color var(--transition-fast);
  flex: 1;
}

.user-info:hover {
  background: var(--sidebar-item-hover);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2rem;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--sidebar-text);
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  margin: 0;
  text-transform: capitalize;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  border-radius: var(--radius-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface-primary);
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.welcome-icon {
  color: var(--color-primary-600);
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
}

.welcome-subtitle {
  color: var(--color-text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 32px 0;
}

.new-conversation-button {
  background: var(--color-primary-600);
  color: var(--color-text-inverse);
  border: none;
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.new-conversation-button:hover {
  background: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

/* Sidebar Content Styles */
.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-section {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sidebar-section:first-child {
  flex: 1;
  min-height: 300px;
}

.sidebar-section:last-child {
  flex: 0 0 auto;
  max-height: 250px;
  border-top: 1px solid var(--sidebar-border);
}

.section-header {
  padding: 12px 16px 8px 16px;
  border-bottom: 1px solid var(--sidebar-border);
  background: var(--sidebar-background);
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
</style>
