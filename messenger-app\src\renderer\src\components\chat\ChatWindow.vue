<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { useChatStore } from '../../stores/chat'
import { useAuthStore } from '../../stores/auth'
import type { Message, MessageAttachment as MessageAttachmentType } from '../../stores/chat'
import FileUpload from '../FileUpload.vue'
import MessageAttachmentComponent from '../MessageAttachment.vue'
import VideoCall from '../call/VideoCall.vue'
import IncomingCallNotification from '../call/IncomingCallNotification.vue'
import EmojiPicker from '../reactions/EmojiPicker.vue'
import MessageReactions from '../reactions/MessageReactions.vue'
import ReplyPreview from '../replies/ReplyPreview.vue'
import ReplyIndicator from '../replies/ReplyIndicator.vue'
import ThemeToggle from '../theme/ThemeToggle.vue'
import { webrtcService } from '../../services/webrtcService'

const chatStore = useChatStore()
const authStore = useAuthStore()

const messageInput = ref('')
const messagesContainer = ref<HTMLElement>()
const isTyping = ref(false)
const typingTimeout = ref<NodeJS.Timeout>()
const showFileUpload = ref(false)
const showEmojiPicker = ref(false)
const emojiPickerMessageId = ref<number | null>(null)

const conversationName = computed(() => {
  if (!chatStore.activeConversation) return ''
  
  if (chatStore.activeConversation.type === 'group' && chatStore.activeConversation.name) {
    return chatStore.activeConversation.name
  }

  // For direct conversations, show the other participant's name
  const otherParticipant = chatStore.activeConversation.participants.find(
    p => p.id !== authStore.user?.id
  )
  return otherParticipant?.display_name || 'Unknown User'
})

const conversationStatus = computed(() => {
  if (!chatStore.activeConversation) return ''

  if (chatStore.activeConversation.type === 'group') {
    const onlineCount = chatStore.activeConversation.participants.filter(p => p.status === 'online').length
    return `${chatStore.activeConversation.participants.length} members, ${onlineCount} online`
  }

  const otherParticipant = chatStore.activeConversation.participants.find(
    p => p.id !== authStore.user?.id
  )
  return otherParticipant?.status || 'offline'
})

const otherParticipant = computed(() => {
  if (!chatStore.activeConversation || chatStore.activeConversation.type === 'group') return null

  return chatStore.activeConversation.participants.find(
    p => p.id !== authStore.user?.id
  )
})

const messages = computed(() => {
  return chatStore.activeConversationMessages
})

const typingUsers = computed(() => {
  if (!chatStore.activeConversation) return []
  return chatStore.getTypingUsersForConversation(chatStore.activeConversation.id)
})

const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Use the new handleSendMessage function that supports replies
const sendMessage = handleSendMessage

const handleInputChange = () => {
  if (!chatStore.activeConversation) return

  // Start typing indicator
  if (!isTyping.value) {
    isTyping.value = true
    chatStore.startTyping(chatStore.activeConversation.id)
  }

  // Reset typing timeout
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
  }

  typingTimeout.value = setTimeout(() => {
    if (isTyping.value) {
      isTyping.value = false
      chatStore.stopTyping(chatStore.activeConversation!.id)
    }
  }, 2000)
}

const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const formatMessageDate = (timestamp: string): string => {
  const date = new Date(timestamp)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString()
  }
}

const shouldShowDateSeparator = (message: Message, index: number): boolean => {
  if (index === 0) return true
  
  const currentDate = new Date(message.created_at).toDateString()
  const previousDate = new Date(messages.value[index - 1].created_at).toDateString()
  
  return currentDate !== previousDate
}

const shouldShowSenderInfo = (message: Message, index: number): boolean => {
  if (index === 0) return true
  
  const previousMessage = messages.value[index - 1]
  return (
    previousMessage.sender.id !== message.sender.id ||
    new Date(message.created_at).getTime() - new Date(previousMessage.created_at).getTime() > 300000 // 5 minutes
  )
}

const isOwnMessage = (message: Message): boolean => {
  return message.sender.id === authStore.user?.id
}

const getMessageAvatarUrl = (message: Message): string | null => {
  return message.sender.avatar_url || null
}

const getMessageInitials = (message: Message): string => {
  return message.sender.display_name.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2)
}

// Watch for new messages and scroll to bottom
watch(messages, () => {
  scrollToBottom()
}, { deep: true })

const toggleFileUpload = () => {
  showFileUpload.value = !showFileUpload.value
}

const handleFilesUploaded = (files: MessageAttachmentType[]) => {
  // Files are uploaded but not sent yet
  console.log('Files uploaded:', files)
}

const handleFilesSent = async (files: MessageAttachmentType[]) => {
  await handleSendWithAttachments(files)
}

// Call functions
const startVoiceCall = async () => {
  if (!otherParticipant.value) {
    console.warn('Cannot start call: no other participant found')
    return
  }

  try {
    await webrtcService.initiateCall(
      otherParticipant.value.id.toString(),
      otherParticipant.value.display_name,
      false // voice call
    )
  } catch (error) {
    console.error('Failed to start voice call:', error)
  }
}

const startVideoCall = async () => {
  if (!otherParticipant.value) {
    console.warn('Cannot start call: no other participant found')
    return
  }

  try {
    await webrtcService.initiateCall(
      otherParticipant.value.id.toString(),
      otherParticipant.value.display_name,
      true // video call
    )
  } catch (error) {
    console.error('Failed to start video call:', error)
  }
}

// Reaction functions
const showEmojiPickerForMessage = (messageId: number) => {
  emojiPickerMessageId.value = messageId
  showEmojiPicker.value = true
}

const hideEmojiPicker = () => {
  showEmojiPicker.value = false
  emojiPickerMessageId.value = null
}

const handleEmojiSelect = async (emoji: string) => {
  if (emojiPickerMessageId.value) {
    await chatStore.addReaction(emojiPickerMessageId.value, emoji)
  }
  hideEmojiPicker()
}

const handleToggleReaction = async (messageId: number, emoji: string) => {
  await chatStore.addReaction(messageId, emoji)
}

// Reply functions
const handleReplyToMessage = (message: Message) => {
  chatStore.setReplyToMessage(message)
  // Focus the message input
  nextTick(() => {
    const messageInputElement = document.querySelector('.message-input') as HTMLInputElement
    if (messageInputElement) {
      messageInputElement.focus()
    }
  })
}

const handleCancelReply = () => {
  chatStore.clearReplyToMessage()
}

const handleSendMessage = async () => {
  if (!messageInput.value.trim() || !chatStore.activeConversation) return

  const content = messageInput.value.trim()
  messageInput.value = ''

  // Stop typing indicator
  if (isTyping.value) {
    chatStore.stopTyping(chatStore.activeConversation.id)
    isTyping.value = false
  }

  // Send message (with reply if applicable)
  if (chatStore.replyToMessage) {
    await chatStore.sendReply(chatStore.activeConversation.id, content)
  } else {
    await chatStore.sendMessage(chatStore.activeConversation.id, content)
  }
}

const handleSendWithAttachments = async (files: MessageAttachmentType[]) => {
  if (!chatStore.activeConversation) return

  const content = messageInput.value.trim()
  messageInput.value = ''
  showFileUpload.value = false

  // Stop typing indicator
  if (isTyping.value) {
    chatStore.stopTyping(chatStore.activeConversation.id)
    isTyping.value = false
  }

  // Send message with attachments (with reply if applicable)
  if (chatStore.replyToMessage) {
    await chatStore.sendReplyWithAttachments(chatStore.activeConversation.id, content, files)
  } else {
    await chatStore.sendMessageWithAttachments(chatStore.activeConversation.id, content, files)
  }
}

// Watch for active conversation changes
watch(() => chatStore.activeConversation, () => {
  scrollToBottom()
})

onMounted(async () => {
  scrollToBottom()

  // Initialize WebRTC peer
  if (authStore.user?.id) {
    try {
      await webrtcService.initializePeer(authStore.user.id.toString())
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error)
    }
  }
})

onUnmounted(() => {
  if (typingTimeout.value) {
    clearTimeout(typingTimeout.value)
  }

  // Stop typing indicator when leaving
  if (isTyping.value && chatStore.activeConversation) {
    chatStore.stopTyping(chatStore.activeConversation.id)
  }

  // Cleanup WebRTC
  webrtcService.cleanup()
})
</script>

<template>
  <div class="chat-window">
    <!-- Chat Header -->
    <div class="chat-header">
      <div class="conversation-info">
        <h2 class="conversation-name">{{ conversationName }}</h2>
        <p class="conversation-status">{{ conversationStatus }}</p>
      </div>
      
      <div class="chat-actions">
        <button
          class="action-button"
          title="Voice Call"
          @click="startVoiceCall"
          :disabled="!otherParticipant || webrtcService.callState.isInCall"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
          </svg>
        </button>

        <button
          class="action-button"
          title="Video Call"
          @click="startVideoCall"
          :disabled="!otherParticipant || webrtcService.callState.isInCall"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="23 7 16 12 23 17 23 7"/>
            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
          </svg>
        </button>

        <ThemeToggle />

        <button class="action-button" title="More Options">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="1"/>
            <circle cx="19" cy="12" r="1"/>
            <circle cx="5" cy="12" r="1"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Messages Area -->
    <div ref="messagesContainer" class="messages-container">
      <div v-if="messages.length === 0" class="empty-messages">
        <div class="empty-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
        </div>
        <p class="empty-text">No messages yet. Start the conversation!</p>
      </div>

      <div v-else class="messages-list">
        <template v-for="(message, index) in messages" :key="message.id">
          <!-- Date Separator -->
          <div v-if="shouldShowDateSeparator(message, index)" class="date-separator">
            <span class="date-text">{{ formatMessageDate(message.created_at) }}</span>
          </div>

          <!-- Message -->
          <div 
            class="message-wrapper"
            :class="{ 'own-message': isOwnMessage(message) }"
          >
            <!-- Avatar (for other users' messages) -->
            <div 
              v-if="!isOwnMessage(message) && shouldShowSenderInfo(message, index)"
              class="message-avatar"
            >
              <img 
                v-if="getMessageAvatarUrl(message)"
                :src="getMessageAvatarUrl(message)!"
                :alt="message.sender.display_name"
                class="avatar-image"
              />
              <div v-else class="avatar-placeholder">
                {{ getMessageInitials(message) }}
              </div>
            </div>
            <div v-else-if="!isOwnMessage(message)" class="message-avatar-spacer"></div>

            <!-- Message Content -->
            <div class="message-content">
              <!-- Sender Info -->
              <div 
                v-if="!isOwnMessage(message) && shouldShowSenderInfo(message, index) && chatStore.activeConversation?.type === 'group'"
                class="message-sender"
              >
                {{ message.sender.display_name }}
              </div>

              <!-- Reply Indicator -->
              <ReplyIndicator
                v-if="message.reply_to_message"
                :reply-to-message="message.reply_to_message"
                @click-reply="() => {}"
              />

              <!-- Message Bubble -->
              <div class="message-bubble">
                <p v-if="message.content" class="message-text">{{ message.content }}</p>

                <!-- Message Attachments -->
                <div v-if="message.attachments && message.attachments.length > 0" class="message-attachments">
                  <MessageAttachmentComponent
                    v-for="attachment in message.attachments"
                    :key="attachment.id"
                    :attachment="attachment"
                  />
                </div>

                <span class="message-time">{{ formatMessageTime(message.created_at) }}</span>
              </div>

              <!-- Message Reactions -->
              <MessageReactions
                v-if="message.reactions && message.reactions.length > 0"
                :reactions="message.reactions"
                :message-id="message.id"
                @toggle-reaction="handleToggleReaction"
              />

              <!-- Message Actions -->
              <div class="message-actions">
                <button
                  class="reply-btn"
                  @click="handleReplyToMessage(message)"
                  title="Reply to message"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 10h10a8 8 0 0 1 8 8v2"></path>
                    <path d="m3 10 6 6"></path>
                    <path d="m3 10 6-6"></path>
                  </svg>
                </button>
                <button
                  class="add-reaction-btn"
                  @click="showEmojiPickerForMessage(message.id)"
                  title="Add reaction"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                    <line x1="9" y1="9" x2="9.01" y2="9"></line>
                    <line x1="15" y1="9" x2="15.01" y2="9"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </template>

        <!-- Typing Indicator -->
        <div v-if="typingUsers.length > 0" class="typing-indicator">
          <div class="typing-avatar">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div class="typing-text">
            {{ typingUsers.length === 1 ? 'Someone is typing...' : `${typingUsers.length} people are typing...` }}
          </div>
        </div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="message-input-container">
      <!-- Reply Preview -->
      <ReplyPreview
        :reply-message="chatStore.replyToMessage"
        @close="handleCancelReply"
      />

      <div class="input-wrapper">
        <button
          class="attachment-button"
          :class="{ active: showFileUpload }"
          title="Attach File"
          @click="toggleFileUpload"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"/>
          </svg>
        </button>
        
        <input
          v-model="messageInput"
          type="text"
          placeholder="Type a message..."
          class="message-input"
          @keydown.enter="sendMessage"
          @input="handleInputChange"
        />
        
        <button 
          class="send-button"
          :disabled="!messageInput.trim()"
          @click="sendMessage"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="22" y1="2" x2="11" y2="13"/>
            <polygon points="22,2 15,22 11,13 2,9 22,2"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- File Upload -->
    <div v-if="showFileUpload" class="file-upload-panel">
      <FileUpload
        @files-uploaded="handleFilesUploaded"
        @files-sent="handleFilesSent"
      />
    </div>

    <!-- Video Call Interface -->
    <VideoCall />

    <!-- Incoming Call Notification -->
    <IncomingCallNotification />

    <!-- Emoji Picker -->
    <EmojiPicker
      :show="showEmojiPicker"
      @close="hideEmojiPicker"
      @select="handleEmojiSelect"
    />
  </div>
</template>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--chat-background);
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-surface-primary);
}

.conversation-info {
  flex: 1;
}

.conversation-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 4px 0;
}

.conversation-status {
  font-size: 0.85rem;
  color: var(--color-text-tertiary);
  margin: 0;
  text-transform: capitalize;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--color-text-tertiary);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.action-button:hover:not(:disabled) {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-tertiary);
  text-align: center;
}

.empty-icon {
  color: var(--color-text-muted);
  margin-bottom: 16px;
}

.empty-text {
  margin: 0;
  font-size: 1rem;
}

.messages-list {
  padding: 0 20px;
}

.date-separator {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.date-text {
  background: #f8f9fa;
  color: #666;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.message-wrapper {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-end;
}

.message-wrapper.own-message {
  justify-content: flex-end;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
  flex-shrink: 0;
}

.message-avatar-spacer {
  width: 40px;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

.message-content {
  max-width: 70%;
}

.own-message .message-content {
  max-width: 70%;
}

.message-sender {
  font-size: 0.8rem;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 4px;
}

.message-bubble {
  background: var(--chat-message-other);
  color: var(--chat-message-text-other);
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.own-message .message-bubble {
  background: var(--chat-message-own);
  color: var(--chat-message-text-own);
}

.message-text {
  margin: 0 0 4px 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
}

.typing-indicator {
  display: flex;
  align-items: center;
  margin: 16px 0;
  opacity: 0.7;
}

.typing-avatar {
  width: 32px;
  height: 32px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: #666;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.typing-text {
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
}

.message-input-container {
  padding: 16px 20px;
  border-top: 1px solid var(--color-border-primary);
  background: var(--chat-input-background);
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--color-surface-secondary);
  border: 1px solid var(--chat-input-border);
  border-radius: 24px;
  padding: 8px 12px;
}

.attachment-button {
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  color: var(--color-text-tertiary);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.attachment-button:hover {
  background: var(--color-primary-100);
  color: var(--color-primary-600);
}

.message-input {
  flex: 1;
  border: none;
  background: none;
  color: var(--input-text);
  padding: 8px 12px;
  font-size: 1rem;
  outline: none;
}

.message-input::placeholder {
  color: var(--input-placeholder);
}

.send-button {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  background: var(--button-primary-hover);
  transform: scale(1.05);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.attachment-button.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.file-upload-panel {
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
  padding: 16px 20px;
}

.message-attachments {
  margin: 8px 0;
}

.message-attachments:first-child {
  margin-top: 0;
}

.message-attachments:last-child {
  margin-bottom: 0;
}

/* Message Actions */
.message-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.add-reaction-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.add-reaction-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.reply-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.reply-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .add-reaction-btn {
    color: #9ca3af;
  }

  .add-reaction-btn:hover {
    background: #374151;
    color: #f9fafb;
  }

  .reply-btn {
    color: #9ca3af;
  }

  .reply-btn:hover {
    background: #374151;
    color: #f9fafb;
  }
}
</style>
