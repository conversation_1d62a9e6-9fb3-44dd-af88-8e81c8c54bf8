import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { io, Socket } from 'socket.io-client'

export interface User {
  id: number
  username: string
  email: string
  display_name: string
  avatar_url?: string
  status: 'online' | 'offline' | 'away' | 'busy'
  last_seen: string
  created_at: string
}

export interface AuthResponse {
  user: User
  token: string
  expires_at: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  display_name: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const socket = ref<Socket | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!user.value && !!token.value)

  // API Base URL
  const API_BASE = 'http://localhost:3001/api'

  // Actions
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Login failed')
      }

      if (data.success && data.data) {
        user.value = data.data.user
        token.value = data.data.token
        
        // Store in localStorage
        localStorage.setItem('auth_token', data.data.token)
        localStorage.setItem('user_data', JSON.stringify(data.data.user))

        // Initialize socket connection
        await initializeSocket()

        return true
      }

      throw new Error('Invalid response format')
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData: RegisterData): Promise<boolean> => {
    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed')
      }

      if (data.success && data.data) {
        user.value = data.data.user
        token.value = data.data.token
        
        // Store in localStorage
        localStorage.setItem('auth_token', data.data.token)
        localStorage.setItem('user_data', JSON.stringify(data.data.user))

        // Initialize socket connection
        await initializeSocket()

        return true
      }

      throw new Error('Invalid response format')
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Registration failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    isLoading.value = true

    try {
      if (token.value) {
        await fetch(`${API_BASE}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`,
            'Content-Type': 'application/json',
          },
        })
      }
    } catch (err) {
      console.error('Logout API call failed:', err)
    } finally {
      // Clear state regardless of API call success
      user.value = null
      token.value = null
      error.value = null
      
      // Clear localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')

      // Disconnect socket
      if (socket.value) {
        socket.value.disconnect()
        socket.value = null
      }

      isLoading.value = false
    }
  }

  const checkAuth = async (): Promise<void> => {
    const storedToken = localStorage.getItem('auth_token')
    const storedUser = localStorage.getItem('user_data')

    if (!storedToken || !storedUser) {
      return
    }

    try {
      // Verify token with server
      const response = await fetch(`${API_BASE}/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${storedToken}`,
        },
      })

      if (response.ok) {
        token.value = storedToken
        user.value = JSON.parse(storedUser)
        
        // Initialize socket connection
        await initializeSocket()
      } else {
        // Token is invalid, clear storage
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
      }
    } catch (err) {
      console.error('Auth check failed:', err)
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    }
  }

  const updateProfile = async (updates: Partial<User>): Promise<boolean> => {
    if (!token.value) return false

    isLoading.value = true
    error.value = null

    try {
      const response = await fetch(`${API_BASE}/users/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token.value}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Profile update failed')
      }

      if (data.success && data.data) {
        user.value = { ...user.value!, ...data.data }
        localStorage.setItem('user_data', JSON.stringify(user.value))
        return true
      }

      return false
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Profile update failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const initializeSocket = async (): Promise<void> => {
    if (!token.value || socket.value?.connected) {
      return
    }

    try {
      socket.value = io('http://localhost:3001', {
        auth: {
          token: token.value
        },
        transports: ['websocket', 'polling']
      })

      socket.value.on('connect', () => {
        console.log('Socket connected successfully')
      })

      socket.value.on('authenticated', (data) => {
        console.log('Socket authenticated:', data.user)
      })

      socket.value.on('auth_error', (error) => {
        console.error('Socket authentication error:', error)
        logout()
      })

      socket.value.on('disconnect', () => {
        console.log('Socket disconnected')
      })

      socket.value.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
      })

    } catch (err) {
      console.error('Socket initialization failed:', err)
    }
  }

  const clearError = (): void => {
    error.value = null
  }

  return {
    // State
    user,
    token,
    isLoading,
    error,
    socket,
    
    // Computed
    isAuthenticated,
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    updateProfile,
    initializeSocket,
    clearError
  }
})
