<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from './stores/auth'
import LoginForm from './components/auth/LoginForm.vue'
import RegisterForm from './components/auth/RegisterForm.vue'
import ChatLayout from './components/chat/ChatLayout.vue'

const authStore = useAuthStore()
const showRegister = ref(false)

onMounted(() => {
  // Check if user is already authenticated
  authStore.checkAuth()
})

const toggleAuthMode = () => {
  showRegister.value = !showRegister.value
}
</script>

<template>
  <div id="app" class="app">
    <!-- Authentication Views -->
    <div v-if="!authStore.isAuthenticated" class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1 class="app-title">Messenger</h1>
          <p class="app-subtitle">Connect with friends and family</p>
        </div>

        <div class="auth-content">
          <LoginForm v-if="!showRegister" @switch-to-register="toggleAuthMode" />
          <RegisterForm v-else @switch-to-login="toggleAuthMode" />
        </div>
      </div>
    </div>

    <!-- Main Chat Application -->
    <ChatLayout v-else />
  </div>
</template>

<style scoped>
.app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.app-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-subtitle {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.auth-content {
  animation: fadeIn 0.4s ease-out 0.1s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
