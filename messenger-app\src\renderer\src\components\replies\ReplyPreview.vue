<template>
  <div v-if="replyMessage" class="reply-preview">
    <div class="reply-header">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 10h10a8 8 0 0 1 8 8v2"></path>
        <path d="m3 10 6 6"></path>
        <path d="m3 10 6-6"></path>
      </svg>
      <span class="reply-label">Replying to {{ replyMessage.sender.display_name }}</span>
      <button class="close-reply-btn" @click="$emit('close')" title="Cancel reply">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>
    <div class="reply-content">
      <p class="reply-text">{{ truncateText(replyMessage.content, 100) }}</p>
      <div v-if="replyMessage.attachments && replyMessage.attachments.length > 0" class="reply-attachments">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
        </svg>
        <span>{{ replyMessage.attachments.length }} attachment{{ replyMessage.attachments.length > 1 ? 's' : '' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Message } from '../../stores/chat'

interface Props {
  replyMessage: Message | null
}

defineProps<Props>()
defineEmits<{
  close: []
}>()

const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
</script>

<style scoped>
.reply-preview {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  position: relative;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 500;
}

.reply-header svg {
  flex-shrink: 0;
}

.reply-label {
  flex: 1;
}

.close-reply-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-reply-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.reply-content {
  padding-left: 24px;
}

.reply-text {
  margin: 0;
  color: #4b5563;
  font-size: 0.9rem;
  line-height: 1.4;
}

.reply-attachments {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  color: #6b7280;
  font-size: 0.8rem;
}

.reply-attachments svg {
  flex-shrink: 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .reply-preview {
    background: #374151;
    border-color: #4b5563;
  }
  
  .reply-header {
    color: #9ca3af;
  }
  
  .reply-text {
    color: #d1d5db;
  }
  
  .reply-attachments {
    color: #9ca3af;
  }
  
  .close-reply-btn {
    color: #9ca3af;
  }
  
  .close-reply-btn:hover {
    background: #4b5563;
    color: #f9fafb;
  }
}
</style>
